package config

import (
	"fmt"

	"arboris/src/common/loggeri"
	"arboris/src/toolkits/i18n"

	"github.com/toolkits/pkg/file"
)

type ConfigT struct {
	Logger     loggeri.Config    `yaml:"logger"`
	HTTP       httpSection       `yaml:"http"`
	I18n       i18n.I18nSection  `yaml:"i18n"`
	JumpServer jumpServerSection `yaml:"jumpserver"`
}

type httpSection struct {
	Mode string `yaml:"mode"`
}

type jumpServerSection struct {
	BaseURL       string                     `yaml:"base_url"`
	Username      string                     `yaml:"username"`
	Password      string                     `yaml:"password"`
	Token         string                     `yaml:"token"`
	Organization  string                     `yaml:"organization"`
	Timeout       int                        `yaml:"timeout"`
	NodeConfig    jumpServerNodeConfig       `yaml:"node_config"`
	AssetConfig   jumpServerAssetConfig      `yaml:"asset_config"`
	PermConfig    jumpServerPermConfig       `yaml:"permission_config"`
	AccountConfig JumpServerAccountConfig    `yaml:"account_config"`
}

type jumpServerNodeConfig struct {
	RootNodeName string `yaml:"root_node_name"`
}

type jumpServerAssetConfig struct {
	DefaultPlatform string `yaml:"default_platform"`
	DefaultPort     int    `yaml:"default_port"`
	DefaultProtocol string `yaml:"default_protocol"`
}

type jumpServerPermConfig struct {
	PermissionPrefix   string   `yaml:"permission_prefix"`
	DefaultAccounts    []string `yaml:"default_accounts"`
	DefaultProtocols   []string `yaml:"default_protocols"`
	DefaultExpiredDate string   `yaml:"default_expired_date"`
}

type JumpServerAccountConfig struct {
	EnableAutoBinding bool                    `yaml:"enable_auto_binding"`
	FallbackStrategy  FallbackStrategyConfig  `yaml:"fallback_strategy"`
	SyncConfig        AccountSyncConfig       `yaml:"sync_config"`
}

type FallbackStrategyConfig struct {
	DefaultTemplate   string `yaml:"default_template"`
	UseParentTemplate bool   `yaml:"use_parent_template"`
}

type AccountSyncConfig struct {
	BindOnCreate   bool `yaml:"bind_on_create"`
	RebindOnUpdate bool `yaml:"rebind_on_update"`
}

var Config *ConfigT

// Parse configuration file
func Parse() error {
	ymlFile := getYmlFile()
	if ymlFile == "" {
		return fmt.Errorf("configuration file not found")
	}

	var c ConfigT
	err := file.ReadYaml(ymlFile, &c)
	if err != nil {
		return fmt.Errorf("cannot read yml[%s]: %v", ymlFile, err)
	}

	Config = &c
	if Config.I18n.DictPath == "" {
		Config.I18n.DictPath = "etc/dict.json"
	}

	// 设置JumpServer配置的默认值
	setJumpServerDefaults(&Config.JumpServer)

	if Config.I18n.Lang == "" {
		Config.I18n.Lang = "zh"
	}

	fmt.Println("config.file:", ymlFile)

	return nil
}

func getYmlFile() string {
	yml := "etc/ams.local.yml"
	if file.IsExist(yml) {
		return yml
	}

	yml = "etc/ams.yml"
	if file.IsExist(yml) {
		return yml
	}

	return ""
}

// setJumpServerDefaults 设置JumpServer配置的默认值
func setJumpServerDefaults(js *jumpServerSection) {
	// 节点配置默认值
	if js.NodeConfig.RootNodeName == "" {
		js.NodeConfig.RootNodeName = "jump-ams"
	}

	// 资产配置默认值
	if js.AssetConfig.DefaultPlatform == "" {
		js.AssetConfig.DefaultPlatform = "Linux"
	}
	if js.AssetConfig.DefaultPort == 0 {
		js.AssetConfig.DefaultPort = 22
	}
	if js.AssetConfig.DefaultProtocol == "" {
		js.AssetConfig.DefaultProtocol = "ssh"
	}

	// 权限配置默认值
	if js.PermConfig.PermissionPrefix == "" {
		js.PermConfig.PermissionPrefix = "perf-"
	}
	if len(js.PermConfig.DefaultAccounts) == 0 {
		js.PermConfig.DefaultAccounts = []string{"@ALL"}
	}
	if len(js.PermConfig.DefaultProtocols) == 0 {
		js.PermConfig.DefaultProtocols = []string{"all"}
	}
	if js.PermConfig.DefaultExpiredDate == "" {
		js.PermConfig.DefaultExpiredDate = "2095-07-15T02:58:22.634069Z"
	}

	// 账号配置默认值
	// 默认启用创建时绑定
	if !js.AccountConfig.EnableAutoBinding {
		js.AccountConfig.SyncConfig.BindOnCreate = true
	}
}
