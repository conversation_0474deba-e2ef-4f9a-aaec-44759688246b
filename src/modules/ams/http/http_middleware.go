package http

import (
	"arboris/src/models"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
)

func shouldBeLogin() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("username", mustUsername(c))
		c.Next()
	}
}

func mustUsername(c *gin.Context) string {
	username := headerUsername(c)

	if username == "" {
		bomb("unauthorized")
	}

	return username
}

func headerUsername(c *gin.Context) string {
	token := c.Get<PERSON>eader("X-User-Token")
	if token == "" {
		return ""
	}

	ut, err := models.UserTokenGet("token=?", token)
	if err != nil {
		logger.Warningf("UserTokenGet[%s] fail: %v", token, err)
		return ""
	}

	if ut == nil {
		return ""
	}

	return ut.Username
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-User-Token")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
