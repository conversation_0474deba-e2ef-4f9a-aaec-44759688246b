package http

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/errors"
	"github.com/toolkits/pkg/logger"

	"arboris/src/toolkits/i18n"
)

// 响应状态码常量
const (
	CodeSuccess      = 0  // 成功
	CodeError        = 1  // 通用错误
	CodeUnauthorized = 2  // 未授权
	CodeNotFound     = 3  // 资源不存在
	CodeValidation   = 4  // 参数验证错误
	CodeDuplicate    = 5  // 重复数据
	CodeServerError  = 6  // 服务器内部错误
)

// ApiResponse 通用API响应结构
type ApiResponse struct {
	Code int         `json:"code" description:"状态码，0表示成功，非0表示错误"`
	Dat  interface{} `json:"dat,omitempty" description:"响应数据"`
	Err  string      `json:"err" description:"错误信息，空字符串表示成功"`
}

// HostListResponse 主机列表响应结构
type HostListResponse struct {
	List  []interface{} `json:"list" description:"主机列表"`
	Total int64         `json:"total" description:"总数量"`
}

func dangerous(v interface{}) {
	errors.Dangerous(v)
}

func bomb(format string, a ...interface{}) {
	errorMsg := i18n.Sprintf(format, a...)
	logger.Errorf("[API Error] %s", errorMsg)
	errors.Bomb(errorMsg)
}

func bind(c *gin.Context, ptr interface{}) {
	dangerous(c.ShouldBindJSON(ptr))
}

func urlParamStr(c *gin.Context, field string) string {
	val := c.Param(field)

	if val == "" {
		bomb("url param[%s] is blank", field)
	}

	return val
}

func urlParamInt64(c *gin.Context, field string) int64 {
	strval := urlParamStr(c, field)
	intval, err := strconv.ParseInt(strval, 10, 64)
	if err != nil {
		bomb("cannot convert %s to int64", strval)
	}

	return intval
}

func urlParamInt(c *gin.Context, field string) int {
	return int(urlParamInt64(c, field))
}

func queryStr(c *gin.Context, key string, defaultVal ...string) string {
	val := c.Query(key)
	if val != "" {
		return val
	}

	if len(defaultVal) == 0 {
		bomb("query param[%s] is necessary", key)
	}

	return defaultVal[0]
}

func queryInt(c *gin.Context, key string, defaultVal ...int) int {
	strv := c.Query(key)
	if strv != "" {
		intv, err := strconv.Atoi(strv)
		if err != nil {
			bomb("cannot convert [%s] to int", strv)
		}
		return intv
	}

	if len(defaultVal) == 0 {
		bomb("query param[%s] is necessary", key)
	}

	return defaultVal[0]
}

func queryInt64(c *gin.Context, key string, defaultVal ...int64) int64 {
	strv := c.Query(key)
	if strv != "" {
		intv, err := strconv.ParseInt(strv, 10, 64)
		if err != nil {
			bomb("cannot convert [%s] to int64", strv)
		}
		return intv
	}

	if len(defaultVal) == 0 {
		bomb("query param[%s] is necessary", key)
	}

	return defaultVal[0]
}

func offset(c *gin.Context, limit int) int {
	if limit <= 0 {
		limit = 10
	}

	page := queryInt(c, "p", 1)
	return (page - 1) * limit
}

func renderMessage(c *gin.Context, v interface{}) {
	if v == nil {
		c.JSON(200, gin.H{"code": CodeSuccess, "err": ""})
		return
	}

	switch t := v.(type) {
	case string:
		c.JSON(200, gin.H{"code": CodeError, "err": t})
	case error:
		c.JSON(200, gin.H{"code": CodeError, "err": t.Error()})
	}
}

func renderData(c *gin.Context, data interface{}, err error) {
	if err == nil {
		c.JSON(200, gin.H{"code": CodeSuccess, "dat": data, "err": ""})
		return
	}

	renderMessage(c, err.Error())
}

func renderZeroPage(c *gin.Context) {
	renderData(c, gin.H{
		"list":  []int{},
		"total": 0,
	}, nil)
}

// renderSuccess 渲染成功响应
func renderSuccess(c *gin.Context, data interface{}) {
	c.JSON(200, gin.H{"code": CodeSuccess, "dat": data, "err": ""})
}

// renderError 渲染错误响应
func renderError(c *gin.Context, code int, message string) {
	c.JSON(200, gin.H{"code": code, "err": message})
}

// renderUnauthorized 渲染未授权响应
func renderUnauthorized(c *gin.Context, message string) {
	if message == "" {
		message = "unauthorized"
	}
	renderError(c, CodeUnauthorized, message)
}

// renderNotFound 渲染资源不存在响应
func renderNotFound(c *gin.Context, message string) {
	if message == "" {
		message = "resource not found"
	}
	renderError(c, CodeNotFound, message)
}

// renderValidationError 渲染参数验证错误响应
func renderValidationError(c *gin.Context, message string) {
	renderError(c, CodeValidation, message)
}

// renderDuplicateError 渲染重复数据错误响应
func renderDuplicateError(c *gin.Context, message string) {
	renderError(c, CodeDuplicate, message)
}

// renderServerError 渲染服务器内部错误响应
func renderServerError(c *gin.Context, message string) {
	if message == "" {
		message = "internal server error"
	}
	renderError(c, CodeServerError, message)
}
