package http

import (
	"bytes"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
)

// LoggingMiddleware 记录所有API请求的中间件
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 记录请求信息
		logger.Infof("[API Request] %s %s %d %s %s %s",
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
			param.Request.UserAgent(),
		)
		return ""
	})
}

// DetailedLoggingMiddleware 详细的API请求日志中间件
func DetailedLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		// 记录请求体（仅对POST/PUT请求，且大小限制在1KB内）
		var requestBody string
		if c.Request.Method == "POST" || c.Request.Method == "PUT" {
			if c.Request.ContentLength > 0 && c.Request.ContentLength < 1024 {
				bodyBytes, err := io.ReadAll(c.Request.Body)
				if err == nil {
					requestBody = string(bodyBytes)
					// 重新设置请求体，供后续处理使用
					c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
				}
			}
		}

		// 获取用户信息
		username := headerUsername(c)
		if username == "" {
			username = "anonymous"
		}

		// 记录请求开始
		logger.Infof("[API Start] User: %s, Method: %s, Path: %s, IP: %s, UserAgent: %s, Body: %s",
			username, c.Request.Method, c.Request.URL.Path, c.ClientIP(),
			c.Request.UserAgent(), requestBody)

		// 处理请求
		c.Next()

		// 记录请求结束
		duration := time.Since(startTime)
		statusCode := c.Writer.Status()

		if statusCode >= 400 {
			logger.Errorf("[API Error] User: %s, Method: %s, Path: %s, Status: %d, Duration: %v, IP: %s",
				username, c.Request.Method, c.Request.URL.Path, statusCode, duration, c.ClientIP())
		} else {
			logger.Infof("[API Success] User: %s, Method: %s, Path: %s, Status: %d, Duration: %v, IP: %s",
				username, c.Request.Method, c.Request.URL.Path, statusCode, duration, c.ClientIP())
		}
	}
}

// ErrorLoggingMiddleware 错误日志中间件
func ErrorLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			username := headerUsername(c)
			if username == "" {
				username = "anonymous"
			}

			for _, err := range c.Errors {
				logger.Errorf("[API Error] User: %s, Method: %s, Path: %s, Error: %s, IP: %s",
					username, c.Request.Method, c.Request.URL.Path, err.Error(), c.ClientIP())
			}
		}
	}
}

// PerformanceLoggingMiddleware 性能监控中间件
func PerformanceLoggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		startTime := time.Now()

		c.Next()

		duration := time.Since(startTime)

		// 记录慢请求
		if duration > 5*time.Second {
			username := headerUsername(c)
			if username == "" {
				username = "anonymous"
			}

			logger.Warningf("[API Slow] User: %s, Method: %s, Path: %s, Duration: %v, IP: %s",
				username, c.Request.Method, c.Request.URL.Path, duration, c.ClientIP())
		}
	}
}
