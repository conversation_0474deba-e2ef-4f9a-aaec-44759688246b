package http

import (
	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/service"
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
)

// @Summary 手动绑定账号模板
// @Description 为指定资产手动绑定账号模板
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param asset_id query string true "资产ID"
// @Param node_path query string true "节点路径"
// @Success 200 {object} ApiResponse "绑定成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /accounts/bind [post]
func bindAccountTemplate(c *gin.Context) {
	assetID := queryStr(c, "asset_id", "")
	nodePath := queryStr(c, "node_path", "")

	if assetID == "" {
		bomb("asset_id is required")
	}
	if nodePath == "" {
		bomb("node_path is required")
	}

	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		renderMessage(c, err)
		return
	}

	// 调用JumpServer服务的绑定方法
	err = jsService.BindAccountTemplateToAsset(assetID, nodePath)
	if err != nil {
		logger.Errorf("Failed to bind account template for asset %s with node path %s: %v", assetID, nodePath, err)
		renderMessage(c, err)
		return
	}

	logger.Infof("Successfully bound account template for node path %s to asset %s", nodePath, assetID)
	renderMessage(c, nil)
}

// @Summary 获取账号同步状态
// @Description 获取账号绑定同步的状态信息
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} ApiResponse{dat=map[string]interface{}} "同步状态"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /accounts/sync/status [get]
func getAccountSyncStatus(c *gin.Context) {
	status := map[string]interface{}{
		"auto_binding_enabled": config.Config.JumpServer.AccountConfig.EnableAutoBinding,
		"bind_on_create":       config.Config.JumpServer.AccountConfig.SyncConfig.BindOnCreate,
		"rebind_on_update":     config.Config.JumpServer.AccountConfig.SyncConfig.RebindOnUpdate,
		"default_template":     config.Config.JumpServer.AccountConfig.FallbackStrategy.DefaultTemplate,
		"use_parent_template":  config.Config.JumpServer.AccountConfig.FallbackStrategy.UseParentTemplate,
	}

	renderData(c, status, nil)
}

// @Summary 解绑账号
// @Description 解绑指定资产的账号
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param asset_id query string true "资产ID"
// @Param account_id query string true "账号ID"
// @Success 200 {object} ApiResponse "解绑成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /accounts/bindings [delete]
func accountBindingUnbind(c *gin.Context) {
	assetID := queryStr(c, "asset_id", "")
	accountID := queryStr(c, "account_id", "")

	if assetID == "" {
		bomb("asset_id is required")
	}
	if accountID == "" {
		bomb("account_id is required")
	}

	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		renderMessage(c, err)
		return
	}

	err = jsService.GetClient().UnbindAccountFromAsset(assetID, accountID)
	if err != nil {
		logger.Errorf("Failed to unbind account %s from asset %s: %v", accountID, assetID, err)
		renderMessage(c, err)
		return
	}

	logger.Infof("Successfully unbound account %s from asset %s", accountID, assetID)
	renderMessage(c, nil)
}

// @Summary 获取账号绑定列表
// @Description 获取指定资产的账号绑定列表
// @Tags 账号管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param asset_id query string true "资产ID"
// @Success 200 {object} ApiResponse{dat=[]jumpserver.Account} "账号绑定列表"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /accounts/bindings [get]
func accountBindingList(c *gin.Context) {
	assetID := queryStr(c, "asset_id", "")
	if assetID == "" {
		bomb("asset_id is required")
	}

	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		renderMessage(c, err)
		return
	}

	accounts, err := jsService.GetClient().GetAssetAccounts(assetID)
	if err != nil {
		logger.Errorf("Failed to get accounts for asset %s: %v", assetID, err)
		renderMessage(c, err)
		return
	}

	renderData(c, accounts, nil)
}
