package http

import (
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"

	"arboris/src/models"
	"strings"
)

// @Summary 创建主机字段
// @Description 创建新的主机自定义字段
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param field body models.HostField true "字段信息"
// @Success 200 {object} ApiResponse "创建成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/fields [post]
func hostFieldNew(c *gin.Context) {
	//loginUser(c).CheckPermGlobal("ams_host_field_mgr")

	var obj models.HostField
	bind(c, &obj)

	username := headerUsername(c)
	logger.Infof("[HostFieldNew] User: %s, FieldIdent: %s, FieldName: %s, FieldType: %s",
		username, obj.FieldIdent, obj.FieldName, obj.FieldType)

	if obj.FieldIdent == "" {
		bomb("[%s] is blank", "field_ident")
	}

	if obj.FieldName == "" {
		bomb("[%s] is blank", "field_name")
	}

	if obj.FieldType == "" {
		bomb("[%s] is blank", "field_type")
	}

	if obj.FieldCate == "" {
		obj.FieldCate = "Default"
	}

	renderMessage(c, models.HostFieldNew(&obj))
}

// @Summary 获取主机字段列表
// @Description 获取所有主机自定义字段列表
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} ApiResponse{dat=[]models.HostField} "字段列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/fields [get]
func hostFieldsGets(c *gin.Context) {
	lst, err := models.HostFieldGets()
	renderData(c, lst, err)
}

// @Summary 获取单个主机字段
// @Description 根据字段ID获取主机字段详细信息
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "字段ID"
// @Success 200 {object} ApiResponse{dat=models.HostField} "字段信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "字段不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/field/{id} [get]
func hostFieldGet(c *gin.Context) {
	obj, err := models.HostFieldGet("id = ?", urlParamInt64(c, "id"))
	renderData(c, obj, err)
}

// @Summary 更新主机字段
// @Description 更新主机自定义字段信息
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "字段ID"
// @Param field body models.HostField true "字段信息"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/field/{id} [put]
func hostFieldPut(c *gin.Context) {
	//loginUser(c).CheckPermGlobal("ams_host_field_mgr")

	var f models.HostField
	bind(c, &f)

	obj, err := models.HostFieldGet("id = ?", urlParamInt64(c, "id"))
	dangerous(err)

	if obj == nil {
		bomb("no such field")
	}

	if obj.FieldType != f.FieldType {
		bomb("field_type cannot modify")
	}

	obj.FieldName = f.FieldName
	obj.FieldExtra = f.FieldExtra
	obj.FieldRequired = f.FieldRequired
	obj.FieldCate = f.FieldCate

	renderMessage(c, obj.Update("field_name", "field_extra", "field_required", "field_cate"))
}

// @Summary 删除主机字段
// @Description 删除主机自定义字段
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "字段ID"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "字段不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/field/{id} [delete]
func hostFieldDel(c *gin.Context) {
	//loginUser(c).CheckPermGlobal("ams_host_field_mgr")

	obj, err := models.HostFieldGet("id = ?", urlParamInt64(c, "id"))
	dangerous(err)

	if obj == nil {
		renderMessage(c, nil)
		return
	}

	renderMessage(c, obj.Del())
}

// @Summary 获取主机字段值
// @Description 获取指定主机的所有自定义字段值
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "主机ID"
// @Success 200 {object} ApiResponse{dat=[]models.HostFieldValue} "字段值列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /host/{id}/fields [get]
func hostFieldGets(c *gin.Context) {
	lst, err := models.HostFieldValueGets(urlParamInt64(c, "id"))
	renderData(c, lst, err)
}

// @Summary 批量更新主机字段值
// @Description 批量更新指定主机的自定义字段值
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "主机ID"
// @Param fields body []models.HostFieldValue true "字段值列表"
// @Success 200 {object} ApiResponse "更新成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /host/{id}/fields [put]
func hostFieldPuts(c *gin.Context) {
	var objs []models.HostFieldValue
	bind(c, &objs)

	//loginUser(c).CheckPermGlobal("ams_host_modify")

	renderMessage(c, models.HostFieldValuePuts(urlParamInt64(c, "id"), objs))
}

type hostFieldValueForm struct {
	HostIds     []int64  `json:"host_ids" binding:"required"`
	FieldIdents []string `json:"field_idents" binding:"required"`
}

// @Summary 批量获取主机字段值
// @Description 获取多个主机的指定自定义字段值
// @Tags 主机字段管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body hostFieldValueForm true "查询条件"
// @Success 200 {object} ApiResponse{dat=map[string]interface{}} "字段值数据"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/fields/values [post]
func hostFieldValueGets(c *gin.Context) {
	var f hostFieldValueForm
	bind(c, &f)

	if len(f.HostIds) == 0 {
		bomb("%s is empty", "host_ids")
	}

	if len(f.FieldIdents) == 0 {
		bomb("%s is empty", "field_idents")
	}

	// 获取分页参数
	limit := queryInt(c, "limit", 20)
	page := queryInt(c, "p", 1)
	start := (page - 1) * limit
	end := page * limit

	// 获取所有字段定义
	fields, err := models.HostFieldGets()
	dangerous(err)

	// 创建字段定义映射
	fieldMap := make(map[string]models.HostField)
	for _, field := range fields {
		fieldMap[field.FieldIdent] = field
	}

	// 验证字段是否都存在
	for _, fieldIdent := range f.FieldIdents {
		if _, exists := fieldMap[fieldIdent]; !exists {
			bomb("field %s not defined", fieldIdent)
		}
	}

	// 获取所有主机
	hosts, err := models.HostByIds(f.HostIds)
	dangerous(err)

	if len(hosts) == 0 {
		renderData(c, gin.H{
			"list":  []interface{}{},
			"total": 0,
		}, nil)
		return
	}

	// 应用分页
	total := len(hosts)
	if start >= total {
		renderData(c, gin.H{
			"list":  []interface{}{},
			"total": total,
		}, nil)
		return
	}

	if end > total {
		end = total
	}

	// 只处理当前页的主机
	pageHosts := hosts[start:end]

	// 构建结果
	result := make([]map[string]interface{}, 0, len(pageHosts))

	// 获取每个主机的字段值
	for _, host := range pageHosts {
		// 获取该主机的所有字段值
		fieldValues, err := models.HostFieldValueGets(host.Id)
		dangerous(err)

		// 创建字段值映射
		valueMap := make(map[string]string)
		for _, fv := range fieldValues {
			valueMap[fv.FieldIdent] = fv.FieldValue
		}

		// 构建主机信息
		hostInfo := map[string]interface{}{
			"host_id":   host.Id,
			"host_name": host.Name,
			"host_ip":   host.IP,
			"fields":    make([]map[string]interface{}, 0, len(f.FieldIdents)),
		}

		// 添加字段值
		for _, fieldIdent := range f.FieldIdents {
			field := fieldMap[fieldIdent]
			value := valueMap[fieldIdent]

			hostInfo["fields"] = append(hostInfo["fields"].([]map[string]interface{}), map[string]interface{}{
				"field_ident": fieldIdent,
				"field_name":  field.FieldName,
				"field_type":  field.FieldType,
				"field_value": value,
			})
		}

		result = append(result, hostInfo)
	}

	renderData(c, gin.H{
		"list":  result,
		"total": total,
	}, nil)
}

type hostFilterForm struct {
	// 基础字段过滤
	IP     []string `json:"ip"`     // IP精确匹配，支持多选
	Ident  string   `json:"ident"`  // 标识符模糊匹配
	Name   string   `json:"name"`   // 名称模糊匹配
	SN     string   `json:"sn"`     // SN模糊匹配
	Cate   []string `json:"cate"`   // 类别精确匹配，支持多选
	Tenant []string `json:"tenant"` // 租户精确匹配，支持多选
	IDC    []string `json:"idc"`    // IDC精确匹配，支持多选
	Model  []string `json:"model"`  // 型号精确匹配，支持多选
	Note   string   `json:"note"`   // 备注模糊匹配
	// 新增字段
	OSVersion     []string `json:"os_version"`     // 操作系统版本精确匹配，支持多选
	KernelVersion []string `json:"kernel_version"` // 内核版本精确匹配，支持多选
	CPUModel      []string `json:"cpu_model"`      // CPU型号精确匹配，支持多选
	GPUModel      []string `json:"gpu_model"`      // GPU型号精确匹配，支持多选
	Zone          []string `json:"zone"`           // 可用区精确匹配，支持多选
	Rack          []string `json:"rack"`           // 机架精确匹配，支持多选

	// 自定义字段过滤
	Fields []struct {
		FieldIdent string   `json:"field_ident"` // 字段标识符
		Values     []string `json:"values"`      // 字段值，支持多选
		IsLike     bool     `json:"is_like"`     // 是否使用模糊匹配
	} `json:"fields"`

	// 分页参数在URL query中
}

// hostFilter 多维度过滤主机列表
func hostFilter(c *gin.Context) {
	var f hostFilterForm
	bind(c, &f)

	// 获取分页参数
	limit := queryInt(c, "limit", 20)
	offset := offset(c, limit)

	// 构建基础查询
	session := models.DB["ams"].Table(new(models.Host))

	// 处理基础字段过滤
	if len(f.IP) > 0 {
		session = session.In("ip", f.IP)
	}
	if f.Ident != "" {
		session = session.Where("ident like ?", "%"+f.Ident+"%")
	}
	if f.Name != "" {
		session = session.Where("name like ?", "%"+f.Name+"%")
	}
	if f.SN != "" {
		session = session.Where("sn like ?", "%"+f.SN+"%")
	}
	if f.Note != "" {
		session = session.Where("note like ?", "%"+f.Note+"%")
	}
	if len(f.Cate) > 0 {
		session = session.In("cate", f.Cate)
	}
	if len(f.Tenant) > 0 {
		session = session.In("tenant", f.Tenant)
	}
	if len(f.IDC) > 0 {
		session = session.In("idc", f.IDC)
	}
	if len(f.Model) > 0 {
		session = session.In("model", f.Model)
	}
	// 处理新增字段过滤
	if len(f.OSVersion) > 0 {
		session = session.In("os_version", f.OSVersion)
	}
	if len(f.KernelVersion) > 0 {
		session = session.In("kernel_version", f.KernelVersion)
	}
	if len(f.CPUModel) > 0 {
		session = session.In("cpu_model", f.CPUModel)
	}
	if len(f.GPUModel) > 0 {
		session = session.In("gpu_model", f.GPUModel)
	}
	if len(f.Zone) > 0 {
		session = session.In("zone", f.Zone)
	}
	if len(f.Rack) > 0 {
		session = session.In("rack", f.Rack)
	}

	// 处理自定义字段过滤
	if len(f.Fields) > 0 {
		// 验证字段是否存在
		fields, err := models.HostFieldGets()
		dangerous(err)

		fieldMap := make(map[string]struct{})
		for _, field := range fields {
			fieldMap[field.FieldIdent] = struct{}{}
		}

		for _, field := range f.Fields {
			if _, exists := fieldMap[field.FieldIdent]; !exists {
				bomb("field %s not defined", field.FieldIdent)
			}
		}

		// 对每个自定义字段条件，通过子查询筛选
		for _, field := range f.Fields {
			subQuery := models.DB["ams"].Table(new(models.HostFieldValue)).Select("host_id")
			subQuery = subQuery.Where("field_ident = ?", field.FieldIdent)

			if field.IsLike {
				// 模糊匹配任一值
				if len(field.Values) > 0 {
					orConditions := make([]string, len(field.Values))
					orArgs := make([]interface{}, len(field.Values))
					for i, v := range field.Values {
						orConditions[i] = "field_value LIKE ?"
						orArgs[i] = "%" + v + "%"
					}
					subQuery = subQuery.Where("("+strings.Join(orConditions, " OR ")+")", orArgs...)
				}
			} else {
				// 精确匹配
				if len(field.Values) > 0 {
					subQuery = subQuery.In("field_value", field.Values)
				}
			}

			// 使用子查询
			var hostIds []int64
			err = subQuery.Find(&hostIds)
			dangerous(err)

			if len(hostIds) > 0 {
				session = session.In("id", hostIds)
			} else {
				// 如果没有匹配的主机ID，返回空结果
				session = session.Where("1 = 0")
			}
		}
	}

	// 获取总数
	total, err := session.Clone().Count()
	dangerous(err)

	// 如果没有数据，直接返回空结果
	if total == 0 {
		renderData(c, gin.H{
			"list":  []interface{}{},
			"total": 0,
		}, nil)
		return
	}

	// 获取分页数据
	var hosts []models.Host
	err = session.Limit(limit, offset).OrderBy("ident").Find(&hosts)
	dangerous(err)

	// 获取主机的自定义字段值
	result := make([]map[string]interface{}, 0, len(hosts))
	for _, host := range hosts {
		// 获取该主机的所有字段值
		fieldValues, err := models.HostFieldValueGets(host.Id)
		dangerous(err)

		// 构建主机信息
		hostInfo := map[string]interface{}{
			"id":             host.Id,
			"ip":             host.IP,
			"ident":          host.Ident,
			"name":           host.Name,
			"sn":             host.SN,
			"cate":           host.Cate,
			"tenant":         host.Tenant,
			"note":           host.Note,
			"model":          host.Model,
			"idc":            host.IDC,
			"gpu":            host.GPU,
			"cpu":            host.CPU,
			"mem":            host.Mem,
			"disk":           host.Disk,
			"os_version":     host.OSVersion,
			"kernel_version": host.KernelVersion,
			"cpu_model":      host.CPUModel,
			"gpu_model":      host.GPUModel,
			"zone":           host.Zone,
			"rack":           host.Rack,
			"field_values":   make(map[string]string),
		}

		// 添加自定义字段值
		for _, fv := range fieldValues {
			hostInfo["field_values"].(map[string]string)[fv.FieldIdent] = fv.FieldValue
		}

		result = append(result, hostInfo)
	}

	renderData(c, gin.H{
		"list":  result,
		"total": total,
	}, nil)
}
