package jumpserver

import (
	"bytes"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"net/url"
	"strings"
	"time"

	"arboris/src/modules/ams/config"
	"github.com/toolkits/pkg/logger"
)

// Client JumpServer API客户端
type Client struct {
	baseURL      string
	username     string
	password     string
	token        string
	organization string
	timeout      time.Duration
	httpClient   *http.Client
}

// NewClient 创建新的JumpServer客户端
func NewClient(baseURL, username, password, token, organization string, timeout time.Duration) *Client {
	return &Client{
		baseURL:      baseURL,
		username:     username,
		password:     password,
		token:        token,
		organization: organization,
		timeout:      timeout,
		httpClient: &http.Client{
			Timeout: timeout,
		},
	}
}

// generateRandomPassword 生成随机密码
func generateRandomPassword(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	password := make([]byte, length)
	for i := range password {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		password[i] = charset[num.Int64()]
	}
	return string(password)
}

// TestConnection 测试连接
func (c *Client) TestConnection() error {
	req, err := http.NewRequest("GET", c.baseURL+"/api/v1/users/profile/", nil)
	if err != nil {
		return err
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("connection failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("authentication failed: status=%d", resp.StatusCode)
	}

	logger.Info("JumpServer connection test successful")
	return nil
}

// CreateUser 创建用户
func (c *Client) CreateUser(user *UserCreateRequest) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/users/users/", c.baseURL)

	var createdUser User
	if err := c.doRequest("POST", url, user, &createdUser); err != nil {
		return nil, err
	}

	return &createdUser, nil
}

// CreateUserWithPassword 创建用户并生成随机密码
func (c *Client) CreateUserWithPassword(user *UserCreateRequest) (*UserCreateResponse, error) {
	// 生成随机密码
	password := generateRandomPassword(12)

	// 第一步：创建用户（不设置密码）
	createURL := fmt.Sprintf("%s/api/v1/users/users/", c.baseURL)

	// 创建用户请求时不包含密码
	createReq := &UserCreateRequest{
		Username: user.Username,
		Name:     user.Name,
		Email:    user.Email,
		Phone:    user.Phone,
		IsActive: user.IsActive,
		// 不设置Password字段
	}

	var createdUser User
	if err := c.doRequest("POST", createURL, createReq, &createdUser); err != nil {
		return nil, fmt.Errorf("failed to create user: %v", err)
	}

	// 第二步：通过PATCH更新密码
	updateURL := fmt.Sprintf("%s/api/v1/users/users/%s/", c.baseURL, createdUser.ID)

	passwordUpdate := map[string]interface{}{
		"password": password,
	}

	var updatedUser User
	if err := c.doRequest("PATCH", updateURL, passwordUpdate, &updatedUser); err != nil {
		return nil, fmt.Errorf("failed to set password: %v", err)
	}

	return &UserCreateResponse{
		User:     &updatedUser,
		Password: password,
	}, nil
}

// DeleteUser 删除用户
func (c *Client) DeleteUser(userID string) error {
	url := fmt.Sprintf("%s/api/v1/users/users/%s/", c.baseURL, userID)
	return c.doRequest("DELETE", url, nil, nil)
}

// UpdateUserStatus 更新用户状态（激活/禁用）
func (c *Client) UpdateUserStatus(userID string, isActive bool) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/users/users/%s/", c.baseURL, userID)

	updateReq := map[string]interface{}{
		"is_active": isActive,
	}

	var updatedUser User
	if err := c.doRequest("PATCH", url, updateReq, &updatedUser); err != nil {
		return nil, err
	}

	return &updatedUser, nil
}

// GetUser 获取用户信息
func (c *Client) GetUser(username string) (*User, error) {
	url := fmt.Sprintf("%s/api/v1/users/users/?username=%s", c.baseURL, url.QueryEscape(username))

	var users []User
	if err := c.doRequest("GET", url, nil, &users); err != nil {
		return nil, err
	}

	if len(users) == 0 {
		return nil, fmt.Errorf("user not found: %s", username)
	}

	return &users[0], nil
}

// CreateUserGroup 创建用户组
func (c *Client) CreateUserGroup(name, comment string) (*UserGroup, error) {
	url := fmt.Sprintf("%s/api/v1/users/groups/", c.baseURL)

	req := map[string]interface{}{
		"name":    name,
		"comment": comment,
	}

	var createdGroup UserGroup
	if err := c.doRequest("POST", url, req, &createdGroup); err != nil {
		return nil, err
	}

	return &createdGroup, nil
}

// GetUserGroup 获取用户组
func (c *Client) GetUserGroup(name string) (*UserGroup, error) {
	url := fmt.Sprintf("%s/api/v1/users/groups/?name=%s", c.baseURL, url.QueryEscape(name))

	var response APIResponse
	if err := c.doRequest("GET", url, nil, &response); err != nil {
		return nil, err
	}

	results, ok := response.Results.([]interface{})
	if !ok || len(results) == 0 {
		return nil, fmt.Errorf("user group not found: %s", name)
	}

	groupData, _ := json.Marshal(results[0])
	var group UserGroup
	if err := json.Unmarshal(groupData, &group); err != nil {
		return nil, fmt.Errorf("failed to parse group data: %v", err)
	}

	return &group, nil
}

// AddUserToGroup 将用户添加到用户组
func (c *Client) AddUserToGroup(groupID, userID string) error {
	url := fmt.Sprintf("%s/api/v1/users/groups/%s/users/", c.baseURL, groupID)

	req := map[string]interface{}{
		"users": []string{userID},
	}

	return c.doRequest("POST", url, req, nil)
}

// GetNodeByValue 根据value获取节点
func (c *Client) GetNodeByValue(key string) (*Node, error) {
	url := fmt.Sprintf("%s/api/v1/assets/nodes/?value=%s", c.baseURL, url.QueryEscape(key))

	var response []Nodes
	if err := c.doRequest("GET", url, nil, &response); err != nil {
		return nil, err
	}

	if len(response) == 0 {
		return nil, fmt.Errorf("node not found: %s", key)
	}

	// 转换为 Node 结构体
	node := &Node{
		ID:    response[0].Id,
		Key:   response[0].Key,
		Value: response[0].Value,
	}

	return node, nil
}

// GetNodes 获取所有节点
func (c *Client) GetNodes() ([]Node, error) {
	url := fmt.Sprintf("%s/api/v1/assets/nodes/", c.baseURL)

	var nodes []Node
	if err := c.doRequest("GET", url, nil, &nodes); err != nil {
		return nil, err
	}

	return nodes, nil
}

// CreateNode 创建节点
func (c *Client) CreateNode(key, value, parent string) (*Node, error) {
	var url string
	var req map[string]interface{}

	if parent == "" {
		// 创建根级节点
		url = fmt.Sprintf("%s/api/v1/assets/nodes/", c.baseURL)
		req = map[string]interface{}{
			"key":   key,
			"value": value,
		}
	} else {
		// 创建子节点，使用 children API
		url = fmt.Sprintf("%s/api/v1/assets/nodes/%s/children/", c.baseURL, parent)
		req = map[string]interface{}{
			"value": value,
		}
	}

	logger.Debugf("Creating node with URL: %s, Request: %+v", url, req)

	var createdNode Node
	if err := c.doRequest("POST", url, req, &createdNode); err != nil {
		logger.Errorf("Failed to create node: URL=%s, Request=%+v, Error=%v", url, req, err)
		return nil, err
	}

	logger.Infof("Successfully created node: %+v", createdNode)
	return &createdNode, nil
}

// GetOrCreateIDCNode 获取或创建IDC子节点
func (c *Client) GetOrCreateIDCNode(idcName string, parentID string) (*Node, error) {
	if idcName == "" {
		// IDC为空，返回父节点信息
		logger.Debug("IDC name is empty, returning parent node")
		return &Node{ID: parentID}, nil
	}

	logger.Infof("GetOrCreateIDCNode: idcName=%s, parentID=%s", idcName, parentID)

	// 1. 先查询是否已存在
	existingNode, err := c.findIDCNode(idcName)
	if err != nil {
		logger.Errorf("Failed to find IDC node '%s': %v", idcName, err)
		return nil, fmt.Errorf("failed to find IDC node: %v", err)
	}

	// 2. 如果存在，直接返回
	if existingNode != nil {
		logger.Warningf("Found existing IDC node: %+v", existingNode)
		return existingNode, nil
	}

	logger.Infof("IDC node '%s' not found, attempting to create", idcName)

	// 3. 如果不存在，尝试创建
	newNode, err := c.CreateNode(idcName, idcName, parentID)
	if err != nil {
		logger.Errorf("Failed to create IDC node '%s': %v", idcName, err)

		// 如果创建失败且是重复名称错误，再次查找（可能是并发创建）
		if strings.Contains(err.Error(), "same level node name cannot be the same") ||
			strings.Contains(err.Error(), "duplicate") ||
			strings.Contains(err.Error(), "already exists") {
			logger.Debugf("Node creation conflict detected, retrying search for '%s'", idcName)

			existingNode, findErr := c.findIDCNode(idcName)
			if findErr != nil {
				return nil, fmt.Errorf("failed to find node after create conflict: %v", findErr)
			}
			if existingNode != nil {
				logger.Debugf("Found node after conflict resolution: %+v", existingNode)
				return existingNode, nil
			}
		}
		return nil, fmt.Errorf("failed to create IDC node '%s': %v", idcName, err)
	}

	logger.Infof("Successfully created IDC node: %+v", newNode)
	return newNode, nil
}

// findIDCNode 查找指定的IDC节点
func (c *Client) findIDCNode(idcName string) (*Node, error) {
	// 使用 value 参数查询指定名称的节点，需要进行URL编码
	encodedIDCName := url.QueryEscape(idcName)
	requestURL := fmt.Sprintf("%s/api/v1/assets/nodes/?value=%s", c.baseURL, encodedIDCName)

	logger.Infof("Searching for IDC node: original='%s', encoded='%s', url='%s'", idcName, encodedIDCName, requestURL)

	var nodes []Nodes
	if err := c.doRequest("GET", requestURL, nil, &nodes); err != nil {
		return nil, fmt.Errorf("failed to query nodes by value: %v", err)
	}

	// 如果没有找到任何节点，返回 nil
	if len(nodes) == 0 {
		return nil, nil
	}

	// 构造期望的 full_value 路径
	// 格式：/DEFAULT/jump-ams/IDC名称
	expectedFullValue := fmt.Sprintf("/DEFAULT/%s/%s", config.Config.JumpServer.NodeConfig.RootNodeName, idcName)

	// 在查询结果中查找匹配的节点
	for _, node := range nodes {
		if node.FullValue == expectedFullValue {
			// 转换为 Node 结构体
			return &Node{
				ID:    node.Id,
				Key:   node.Key,
				Value: node.Value,
			}, nil
		}
	}

	return nil, nil // 未找到匹配的节点
}

// CreateAsset 创建资产（使用新的 hosts 端点）
func (c *Client) CreateAsset(asset *Asset) (*Asset, error) {
	url := fmt.Sprintf("%s/api/v1/assets/hosts/", c.baseURL)

	// 构建主机创建请求
	hostReq := map[string]interface{}{
		"name":     asset.Hostname,
		"address":  asset.IP,
		"platform": 1, // 平台 ID，1通常表示Linux
		"comment":  asset.Comment,
		"nodes":    asset.Nodes, // 添加节点信息
	}

	var createdHost map[string]interface{}
	if err := c.doRequest("POST", url, hostReq, &createdHost); err != nil {
		return nil, err
	}

	// 将响应转换为 Asset 结构
	createdAsset := &Asset{
		ID:       createdHost["id"].(string),
		Hostname: createdHost["name"].(string),
		IP:       createdHost["address"].(string),
		Platform: config.Config.JumpServer.AssetConfig.DefaultPlatform,
		IsActive: true,
		Comment:  asset.Comment,
		Labels:   asset.Labels,
	}

	// 处理协议信息
	if protocols, ok := createdHost["protocols"].([]interface{}); ok {
		for _, p := range protocols {
			if protocol, ok := p.(map[string]interface{}); ok {
				createdAsset.Protocols = append(createdAsset.Protocols, Protocol{
					Name: protocol["name"].(string),
					Port: int(protocol["port"].(float64)),
				})
			}
		}
	}

	// 处理节点信息
	if nodes, ok := createdHost["nodes"].([]interface{}); ok {
		for _, n := range nodes {
			if node, ok := n.(map[string]interface{}); ok {
				createdAsset.Nodes = append(createdAsset.Nodes, node["id"].(string))
			}
		}
	}

	return createdAsset, nil
}

// MoveAssetToNode 将资产移动到节点
func (c *Client) MoveAssetToNode(assetID, nodeID string) error {
	url := fmt.Sprintf("%s/api/v1/assets/assets/%s/", c.baseURL, assetID)

	req := map[string]interface{}{
		"nodes": []string{nodeID},
	}

	return c.doRequest("PATCH", url, req, nil)
}

// DeleteAsset 删除资产
func (c *Client) DeleteAsset(assetID string) error {
	url := fmt.Sprintf("%s/api/v1/assets/assets/%s/", c.baseURL, assetID)
	return c.doRequest("DELETE", url, nil, nil)
}

// UpdateAsset 更新资产
func (c *Client) UpdateAsset(asset *Asset) error {
	url := fmt.Sprintf("%s/api/v1/assets/assets/%s/", c.baseURL, asset.ID)
	return c.doRequest("PUT", url, asset, nil)
}

// CreatePermission 创建权限
func (c *Client) CreatePermission(perm *PermissionCreateRequest) (*Permission, error) {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/", c.baseURL)

	var createdPerm Permission
	if err := c.doRequest("POST", url, perm, &createdPerm); err != nil {
		return nil, err
	}

	return &createdPerm, nil
}

// GetUserPermissions 获取用户权限（实际返回用户可访问的资产列表）
func (c *Client) GetUserPermissions(username string) ([]UserAsset, error) {
	// 首先获取用户信息
	user, err := c.GetUser(username)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s/api/v1/perms/users/%s/assets/", c.baseURL, user.ID)

	logger.Infof("Getting user assets for %s (ID: %s), URL: %s", username, user.ID, url)

	// JumpServer用户资产API直接返回数组格式
	var assets []UserAsset
	if err := c.doRequest("GET", url, nil, &assets); err != nil {
		logger.Errorf("Failed to get user assets for %s: %v", username, err)
		return nil, err
	}

	logger.Infof("Successfully got %d assets for user %s", len(assets), username)
	return assets, nil
}

// GetPermissions 获取权限列表
func (c *Client) GetPermissions() ([]Permission, error) {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/", c.baseURL)

	var response APIResponse
	if err := c.doRequest("GET", url, nil, &response); err != nil {
		return nil, err
	}

	results, ok := response.Results.([]interface{})
	if !ok {
		return []Permission{}, nil
	}

	var permissions []Permission
	for _, result := range results {
		permData, _ := json.Marshal(result)
		var perm Permission
		if err := json.Unmarshal(permData, &perm); err == nil {
			permissions = append(permissions, perm)
		}
	}

	return permissions, nil
}

// DeletePermission 删除权限
func (c *Client) DeletePermission(permissionID string) error {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/%s/", c.baseURL, permissionID)
	return c.doRequest("DELETE", url, nil, nil)
}

// DeletePermissionByName 根据权限名称删除权限
func (c *Client) DeletePermissionByName(permissionName string) error {
	logger.Infof("Deleting permission by name: %s", permissionName)

	// 首先查找权限
	permission, err := c.GetPermissionByName(permissionName)
	if err != nil {
		return fmt.Errorf("failed to find permission %s: %v", permissionName, err)
	}

	if permission == nil {
		logger.Debugf("Permission %s not found, nothing to delete", permissionName)
		return nil // 权限不存在，认为删除成功
	}

	// 删除权限
	logger.Infof("Deleting permission %s with ID %s", permissionName, permission.ID)
	return c.DeletePermission(permission.ID)
}

// RemoveUserFromGroup 从用户组中移除用户
func (c *Client) RemoveUserFromGroup(groupID, userID string) error {
	url := fmt.Sprintf("%s/api/v1/users/groups/%s/users/", c.baseURL, groupID)

	req := map[string]interface{}{
		"users": []string{userID},
	}

	return c.doRequest("DELETE", url, req, nil)
}

// doRequest 执行HTTP请求
func (c *Client) doRequest(method, url string, body interface{}, result interface{}) error {
	logger.Debugf("JumpServer API Request: method=%s, url=%s", method, url)
	startTime := time.Now()

	var reqBody io.Reader
	var bodyStr string
	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			logger.Errorf("Failed to marshal request body: method=%s, url=%s, error=%v", method, url, err)
			return fmt.Errorf("failed to marshal request body: %v", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
		bodyStr = string(jsonData)
		logger.Debugf("JumpServer API Request Body: %s", bodyStr)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		logger.Errorf("Failed to create HTTP request: method=%s, url=%s, error=%v", method, url, err)
		return fmt.Errorf("failed to create request: %v", err)
	}

	c.setHeaders(req)

	resp, err := c.httpClient.Do(req)
	duration := time.Since(startTime)

	if err != nil {
		logger.Errorf("HTTP request failed: method=%s, url=%s, duration=%v, error=%v",
			method, url, duration, err)
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Errorf("Failed to read response body: method=%s, url=%s, status=%d, duration=%v, error=%v",
			method, url, resp.StatusCode, duration, err)
		return fmt.Errorf("failed to read response body: %v", err)
	}

	logger.Debugf("JumpServer API Response: method=%s, url=%s, status=%d, duration=%v, bodyLength=%d",
		method, url, resp.StatusCode, duration, len(respBody))

	if resp.StatusCode >= 400 {
		logger.Errorf("HTTP error response: status=%d, url=%s, body=%s", resp.StatusCode, url, string(respBody))

		// 首先尝试解析标准错误响应格式
		var errorResp ErrorResponse
		if err := json.Unmarshal(respBody, &errorResp); err == nil && errorResp.Detail != "" {
			return fmt.Errorf("API error: %s", errorResp.Detail)
		}

		// 尝试解析字段级错误响应格式
		var fieldErrors FieldErrorResponse
		if err := json.Unmarshal(respBody, &fieldErrors); err == nil && len(fieldErrors) > 0 {
			var errorMsgs []string
			for field, msgs := range fieldErrors {
				for _, msg := range msgs {
					errorMsgs = append(errorMsgs, fmt.Sprintf("%s: %s", field, msg))
				}
			}
			if len(errorMsgs) > 0 {
				return fmt.Errorf("API error: %s", strings.Join(errorMsgs, "; "))
			}
		}

		// 尝试解析通用JSON错误格式
		var genericError map[string]interface{}
		if err := json.Unmarshal(respBody, &genericError); err == nil {
			if msg, ok := genericError["message"].(string); ok && msg != "" {
				return fmt.Errorf("API error: %s", msg)
			}
			if detail, ok := genericError["detail"].(string); ok && detail != "" {
				return fmt.Errorf("API error: %s", detail)
			}
		}

		return fmt.Errorf("HTTP error: status=%d, body=%s", resp.StatusCode, string(respBody))
	}

	if result != nil && method != "DELETE" {
		if err := json.Unmarshal(respBody, result); err != nil {
			return fmt.Errorf("failed to unmarshal response: %v", err)
		}
	}

	return nil
}

// setHeaders 设置请求头
func (c *Client) setHeaders(req *http.Request) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	if c.organization != "" {
		req.Header.Set("X-JMS-ORG", c.organization)
	}

	if c.token != "" {
		req.Header.Set("Authorization", "Token "+c.token)
	} else if c.username != "" && c.password != "" {
		req.SetBasicAuth(c.username, c.password)
	}
}

// CreateOrUpdateUserPermission 创建或更新用户权限
func (c *Client) CreateOrUpdateUserPermission(userID string, permission *Permission) error {
	logger.Infof("CreateOrUpdateUserPermission: userID=%s, permissionName=%s", userID, permission.Name)

	// 首先尝试查找是否已存在同名权限
	existingPermission, err := c.GetPermissionByName(permission.Name)
	if err != nil {
		logger.Errorf("Error searching for permission %s: %v", permission.Name, err)
		return fmt.Errorf("failed to search for existing permission: %v", err)
	}

	if existingPermission != nil {
		logger.Infof("Permission %s already exists with ID %s, updating", permission.Name, existingPermission.ID)
		// 权限已存在，更新权限
		return c.UpdateUserPermission(existingPermission.ID, userID, permission)
	} else {
		logger.Infof("Permission %s does not exist, creating new one", permission.Name)
		// 权限不存在，创建新权限
		return c.CreateUserPermission(userID, permission)
	}
}

// CreateUserPermission 创建用户权限
func (c *Client) CreateUserPermission(userID string, permission *Permission) error {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/", c.baseURL)

	// 构建用户列表，确保包含指定的用户ID
	users := []map[string]string{
		{"pk": userID},
	}

	req := map[string]interface{}{
		"name":       permission.Name,
		"users":      users,
		"assets":     permission.Assets,
		"nodes":      []string{}, // 空节点列表
		"accounts":   permission.Accounts,
		"protocols":  permission.Protocols,
		"actions":    permission.Actions,
		"is_active":  permission.IsActive,
		"date_start": time.Now().UTC().Format("2006-01-02T15:04:05.000000Z"),
	}

	// 设置过期时间，默认为2095年（永不过期）
	if permission.DateExpired != "" {
		req["date_expired"] = permission.DateExpired
	} else {
		req["date_expired"] = config.Config.JumpServer.PermConfig.DefaultExpiredDate
	}

	var result map[string]interface{}
	return c.doRequest("POST", url, req, &result)
}

// UpdateUserPermission 更新用户权限
func (c *Client) UpdateUserPermission(permissionID, userID string, permission *Permission) error {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/%s/", c.baseURL, permissionID)

	logger.Infof("Updating permission %s (ID: %s) for user %s", permission.Name, permissionID, userID)

	// 首先获取现有权限的详细信息
	existingPermission, err := c.GetPermissionByID(permissionID)
	if err != nil {
		logger.Errorf("Failed to get existing permission %s: %v", permissionID, err)
		return fmt.Errorf("failed to get existing permission: %v", err)
	}

	// 合并现有资产和新资产
	assetMap := make(map[string]bool)

	// 添加现有资产
	if existingPermission != nil {
		for _, asset := range existingPermission.Assets {
			assetMap[asset] = true
		}
	}

	// 添加新资产
	for _, asset := range permission.Assets {
		assetMap[asset] = true
	}

	// 转换为数组
	var allAssets []string
	for asset := range assetMap {
		allAssets = append(allAssets, asset)
	}

	// 构建用户列表，确保包含指定的用户ID
	users := []map[string]string{
		{"pk": userID},
	}

	// 确保必需字段不为空
	accounts := permission.Accounts
	if len(accounts) == 0 {
		accounts = config.Config.JumpServer.PermConfig.DefaultAccounts
	}

	protocols := permission.Protocols
	if len(protocols) == 0 {
		protocols = config.Config.JumpServer.PermConfig.DefaultProtocols
	}

	actions := permission.Actions
	if len(actions) == 0 {
		actions = getDefaultActions()
	}

	req := map[string]interface{}{
		"name":       permission.Name,
		"users":      users,
		"assets":     allAssets,  // 使用合并后的资产列表
		"nodes":      []string{}, // 空节点列表
		"accounts":   accounts,
		"protocols":  protocols,
		"actions":    actions,
		"is_active":  permission.IsActive,
		"date_start": time.Now().UTC().Format("2006-01-02T15:04:05.000000Z"),
	}

	// 设置过期时间，默认为2095年（永不过期）
	if permission.DateExpired != "" {
		req["date_expired"] = permission.DateExpired
	} else {
		req["date_expired"] = config.Config.JumpServer.PermConfig.DefaultExpiredDate
	}

	logger.Debugf("Updating permission with %d assets", len(allAssets))

	var result map[string]interface{}
	return c.doRequest("PUT", url, req, &result)
}

// GetPermissionByName 根据名称获取权限
func (c *Client) GetPermissionByName(name string) (*Permission, error) {
	encodedName := url.QueryEscape(name)
	requestURL := fmt.Sprintf("%s/api/v1/perms/asset-permissions/?name=%s", c.baseURL, encodedName)

	logger.Infof("Searching for permission: %s, URL: %s", name, requestURL)

	// JumpServer权限查询API直接返回数组，不是APIResponse格式
	var results []map[string]interface{}
	if err := c.doRequest("GET", requestURL, nil, &results); err != nil {
		logger.Errorf("Failed to search permission %s: %v", name, err)
		return nil, err
	}

	if len(results) == 0 {
		logger.Debugf("Permission %s not found", name)
		return nil, nil // 权限不存在
	}

	logger.Debugf("Found %d permissions with name %s", len(results), name)

	// 解析第一个结果
	permData := results[0]

	permission := &Permission{
		Name:     name,
		IsActive: true,
	}

	// 安全解析ID
	if id, ok := permData["id"].(string); ok {
		permission.ID = id
	} else {
		logger.Errorf("Permission %s missing ID field", name)
		return nil, fmt.Errorf("permission missing ID field")
	}

	// 安全解析is_active
	if isActive, ok := permData["is_active"].(bool); ok {
		permission.IsActive = isActive
	}

	// 解析用户组
	if userGroups, ok := permData["user_groups"].([]interface{}); ok {
		for _, ug := range userGroups {
			if ugStr, ok := ug.(string); ok {
				permission.UserGroups = append(permission.UserGroups, ugStr)
			}
		}
	}

	// 解析资产
	if assets, ok := permData["assets"].([]interface{}); ok {
		for _, asset := range assets {
			if assetStr, ok := asset.(string); ok {
				permission.Assets = append(permission.Assets, assetStr)
			}
		}
	}

	// 解析协议
	if protocols, ok := permData["protocols"].([]interface{}); ok {
		for _, protocol := range protocols {
			if protocolStr, ok := protocol.(string); ok {
				permission.Protocols = append(permission.Protocols, protocolStr)
			}
		}
	}

	logger.Infof("Successfully parsed permission %s with ID %s", name, permission.ID)
	return permission, nil
}

// GetPermissionByID 根据ID获取权限详细信息
func (c *Client) GetPermissionByID(permissionID string) (*Permission, error) {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/%s/", c.baseURL, permissionID)

	logger.Debugf("Getting permission by ID: %s", permissionID)

	var permData map[string]interface{}
	if err := c.doRequest("GET", url, nil, &permData); err != nil {
		logger.Errorf("Failed to get permission %s: %v", permissionID, err)
		return nil, err
	}

	permission := &Permission{
		IsActive: true,
	}

	// 安全解析ID
	if id, ok := permData["id"].(string); ok {
		permission.ID = id
	}

	// 安全解析name
	if name, ok := permData["name"].(string); ok {
		permission.Name = name
	}

	// 安全解析is_active
	if isActive, ok := permData["is_active"].(bool); ok {
		permission.IsActive = isActive
	}

	// 解析资产
	if assets, ok := permData["assets"].([]interface{}); ok {
		for _, asset := range assets {
			if assetStr, ok := asset.(string); ok {
				permission.Assets = append(permission.Assets, assetStr)
			}
		}
	}

	// 解析协议
	if protocols, ok := permData["protocols"].([]interface{}); ok {
		for _, protocol := range protocols {
			if protocolStr, ok := protocol.(string); ok {
				permission.Protocols = append(permission.Protocols, protocolStr)
			}
		}
	}

	logger.Infof("Successfully got permission %s with %d assets", permission.Name, len(permission.Assets))
	return permission, nil
}

// RevokeUserPermissionByAssets 根据资产ID回收用户权限
func (c *Client) RevokeUserPermissionByAssets(userID, permissionName string, assetIDs []string) error {
	logger.Debugf("Revoking user permission: userID=%s, permissionName=%s, assetIDs=%v", userID, permissionName, assetIDs)

	// 1. 获取现有权限
	permission, err := c.GetPermissionByName(permissionName)
	if err != nil {
		return fmt.Errorf("failed to get permission %s: %v", permissionName, err)
	}
	if permission == nil {
		logger.Debugf("Permission %s not found, nothing to revoke", permissionName)
		return nil // 权限不存在，认为已经回收
	}

	logger.Infof("Found permission %s with ID %s", permissionName, permission.ID)

	// 2. 获取权限中的具体资产列表
	currentAssets, err := c.GetPermissionAssets(permission.ID)
	if err != nil {
		return fmt.Errorf("failed to get assets for permission %s: %v", permissionName, err)
	}

	logger.Infof("Current permission has %d assets", len(currentAssets))

	// 3. 从权限中移除指定的资产
	var remainingAssets []string
	assetIDMap := make(map[string]bool)
	for _, assetID := range assetIDs {
		assetIDMap[assetID] = true
	}

	for _, assetID := range currentAssets {
		if !assetIDMap[assetID] {
			remainingAssets = append(remainingAssets, assetID)
		}
	}

	logger.Infof("After removal, %d assets remaining", len(remainingAssets))

	// 4. 如果没有剩余资产，删除整个权限
	if len(remainingAssets) == 0 {
		logger.Debugf("No assets remaining, deleting permission %s", permissionName)
		return c.DeletePermission(permission.ID)
	}

	// 5. 否则更新权限，移除指定资产
	logger.Debugf("Updating permission %s with %d remaining assets", permissionName, len(remainingAssets))
	permission.Assets = remainingAssets
	return c.UpdateUserPermission(permission.ID, userID, permission)
}

// GetPermissionAssets 获取权限的具体资产列表
func (c *Client) GetPermissionAssets(permissionID string) ([]string, error) {
	url := fmt.Sprintf("%s/api/v1/perms/asset-permissions/%s/assets/all/?offset=0&limit=1000&display=1", c.baseURL, permissionID)

	logger.Debugf("Getting permission assets: %s", url)

	var response struct {
		Count    int    `json:"count"`
		Next     string `json:"next"`
		Previous string `json:"previous"`
		Results  []struct {
			Asset        string `json:"asset"`
			AssetDisplay string `json:"asset_display"`
		} `json:"results"`
	}

	if err := c.doRequest("GET", url, nil, &response); err != nil {
		logger.Errorf("Failed to get permission assets for %s: %v", permissionID, err)
		return nil, err
	}

	var assetIDs []string
	for _, result := range response.Results {
		assetIDs = append(assetIDs, result.Asset)
	}

	logger.Infof("Found %d assets in permission %s", len(assetIDs), permissionID)
	return assetIDs, nil
}

// getDefaultActions 获取默认的权限动作
func getDefaultActions() []map[string]interface{} {
	return []map[string]interface{}{
		{"value": "connect", "label": "连接 (所有协议)"},
		{"value": "upload", "label": "上传 (RDP, SFTP)"},
		{"value": "download", "label": "下载 (RDP, SFTP)"},
		{"value": "copy", "label": "复制 (RDP, VNC)"},
		{"value": "paste", "label": "粘贴 (RDP, VNC)"},
		{"value": "delete", "label": "删除 (SFTP)"},
		{"value": "share", "label": "分享 (SSH)"},
	}
}

// AssetExists 检查资产是否存在
func (c *Client) AssetExists(assetID string) (bool, error) {
	url := fmt.Sprintf("%s/api/v1/assets/hosts/%s/", c.baseURL, assetID)

	logger.Debugf("Checking asset existence: %s", assetID)

	var asset map[string]interface{}
	if err := c.doRequest("GET", url, nil, &asset); err != nil {
		// 如果是404错误，说明资产不存在
		if isNotFoundError(err) {
			logger.Debugf("Asset %s does not exist", assetID)
			return false, nil
		}
		logger.Errorf("Failed to check asset %s existence: %v", assetID, err)
		return false, err
	}

	logger.Debugf("Asset %s exists", assetID)
	return true, nil
}

// GetJumpAMSNodeAssets 获取jump-ams节点下的所有资产
func (c *Client) GetJumpAMSNodeAssets() ([]UserAsset, error) {
	logger.Debug("Getting jump-ams node assets")

	// 1. 先获取根节点
	rootNodeName := config.Config.JumpServer.NodeConfig.RootNodeName
	node, err := c.GetNodeByValue(rootNodeName)
	if err != nil {
		return nil, fmt.Errorf("failed to get root node %s: %v", rootNodeName, err)
	}
	if node == nil {
		return nil, fmt.Errorf("root node %s not found", rootNodeName)
	}

	// 2. 获取该节点下的所有资产
	url := fmt.Sprintf("%s/api/v1/assets/hosts/?node=%s&limit=1000", c.baseURL, node.ID)

	logger.Debugf("Getting assets for jump-ams node: %s", node.ID)

	var assets []UserAsset
	if err := c.doRequest("GET", url, nil, &assets); err != nil {
		logger.Errorf("Failed to get jump-ams node assets: %v", err)
		return nil, err
	}

	logger.Debugf("Found %d assets in jump-ams node", len(assets))
	return assets, nil
}

// isNotFoundError 判断是否是404错误
func isNotFoundError(err error) bool {
	// 这里需要根据实际的错误类型来判断
	// 简单的字符串匹配，实际使用时可能需要更精确的判断
	return err != nil && (fmt.Sprintf("%v", err) == "404" ||
		fmt.Sprintf("%v", err) == "Not Found" ||
		fmt.Sprintf("%v", err) == "not found")
}

// GetAccountTemplate 获取账号模板
func (c *Client) GetAccountTemplate(name string) (*AccountTemplate, error) {
	url := fmt.Sprintf("%s/api/v1/accounts/account-templates/?name=%s", c.baseURL, url.QueryEscape(name))

	var templates []AccountTemplate
	if err := c.doRequest("GET", url, nil, &templates); err != nil {
		return nil, err
	}

	if len(templates) == 0 {
		return nil, fmt.Errorf("account template not found: %s", name)
	}

	return &templates[0], nil
}

// CreateAccountTemplate 创建账号模板
func (c *Client) CreateAccountTemplate(req *AccountTemplateCreateRequest) (*AccountTemplate, error) {
	url := fmt.Sprintf("%s/api/v1/accounts/account-templates/", c.baseURL)

	var template AccountTemplate
	if err := c.doRequest("POST", url, req, &template); err != nil {
		return nil, err
	}

	return &template, nil
}

// GetAccountByName 根据名称获取账号
func (c *Client) GetAccountByName(name string) (*Account, error) {
	url := fmt.Sprintf("%s/api/v1/accounts/accounts/?name=%s", c.baseURL, url.QueryEscape(name))

	var accounts []Account
	if err := c.doRequest("GET", url, nil, &accounts); err != nil {
		return nil, err
	}

	if len(accounts) == 0 {
		return nil, fmt.Errorf("account not found: %s", name)
	}

	return &accounts[0], nil
}

// CreateAccount 创建账号
func (c *Client) CreateAccount(req *AccountCreateRequest) (*Account, error) {
	url := fmt.Sprintf("%s/api/v1/accounts/accounts/", c.baseURL)

	var account Account
	if err := c.doRequest("POST", url, req, &account); err != nil {
		return nil, err
	}

	return &account, nil
}

// BindAccountToAsset 绑定账号到资产
func (c *Client) BindAccountToAsset(assetID, accountID string) error {
	url := fmt.Sprintf("%s/api/v1/assets/assets/%s/accounts/", c.baseURL, assetID)

	req := map[string]interface{}{
		"accounts": []string{accountID},
	}

	return c.doRequest("POST", url, req, nil)
}

// BindAccountTemplateToAsset 根据账号模板为资产创建并绑定账号
func (c *Client) BindAccountTemplateToAsset(assetID, templateID string) error {
	// 1. 先获取账号模板信息
	template, err := c.GetAccountTemplateByID(templateID)
	if err != nil {
		return fmt.Errorf("failed to get account template %s: %v", templateID, err)
	}

	// 2. 根据模板创建账号实例
	accountReq := []map[string]interface{}{
		{
			"template":    templateID,
			"name":        template.Name,
			"username":    template.Username,
			"secret_type": template.AuthMethod,
			"privileged":  false,
			"asset":       assetID,
		},
	}

	url := fmt.Sprintf("%s/api/v1/accounts/accounts/", c.baseURL)
	return c.doRequest("POST", url, accountReq, nil)
}

// GetAccountTemplateByID 根据ID获取账号模板
func (c *Client) GetAccountTemplateByID(templateID string) (*AccountTemplate, error) {
	url := fmt.Sprintf("%s/api/v1/accounts/account-templates/%s/", c.baseURL, templateID)

	var template AccountTemplate
	if err := c.doRequest("GET", url, nil, &template); err != nil {
		return nil, err
	}

	return &template, nil
}

// GetAssetAccounts 获取资产绑定的账号
func (c *Client) GetAssetAccounts(assetID string) ([]Account, error) {
	url := fmt.Sprintf("%s/api/v1/assets/assets/%s/accounts/", c.baseURL, assetID)

	var accounts []Account
	if err := c.doRequest("GET", url, nil, &accounts); err != nil {
		return nil, err
	}

	return accounts, nil
}

// UnbindAccountFromAsset 解绑账号
func (c *Client) UnbindAccountFromAsset(assetID, accountID string) error {
	url := fmt.Sprintf("%s/api/v1/assets/assets/%s/accounts/%s/", c.baseURL, assetID, accountID)
	return c.doRequest("DELETE", url, nil, nil)
}

// GetNode 获取节点信息
func (c *Client) GetNode(nodeID string) (*Node, error) {
	url := fmt.Sprintf("%s/api/v1/assets/nodes/%s/", c.baseURL, nodeID)

	var node Node
	if err := c.doRequest("GET", url, nil, &node); err != nil {
		return nil, err
	}

	return &node, nil
}
