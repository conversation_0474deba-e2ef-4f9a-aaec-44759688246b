# AMS模块JumpServer同步日志增强总结

## 概述
为AMS模块中同步JumpServer的调用代码添加了详细的日志输出，涵盖了从HTTP请求到底层API调用的完整链路。

## 修改的文件

### 1. `src/modules/ams/service/jumpserver.go`
**主要增强的方法：**

#### SyncHostToJumpServer
- 添加了同步开始和结束的时间戳记录
- 记录主机基本信息（ID、名称、IP、IDC等）
- 记录节点创建和选择过程
- 记录同步耗时和结果

**日志示例：**
```
INFO: Starting sync host to JumpServer: hostID=123
INFO: Found host in database: hostID=123, name=web-server-01, ip=*************, idc=beijing, ident=web01
INFO: Host has IDC field, creating/getting IDC node: hostID=123, idc=beijing
INFO: Successfully synced host to JumpServer: hostID=123, assetID=asset-456, nodeID=node-789, duration=2.5s
```

#### syncHostToJumpServer (内部方法)
- 记录资产创建的详细配置信息
- 记录JumpServer API调用结果

#### DeleteHostFromJumpServer
- 记录删除操作的开始和结果
- 处理空资产ID的情况

#### UpdateHostInJumpServer
- 记录更新操作的详细信息
- 记录资产配置和API调用结果

#### GetUserHosts
- 记录用户权限查询过程
- 统计资产数量和主机匹配情况

### 2. `src/modules/ams/service/sync_manager.go`
**主要增强的方法：**

#### CreateHostWithSync
- 记录同步管理器层面的操作
- 添加耗时统计
- 详细记录同步状态变化

**日志示例：**
```
INFO: SyncManager: Starting sync host to JumpServer: hostID=123, ip=*************, name=web-server-01
INFO: SyncManager: Host sync completed successfully: hostID=123, assetID=asset-456, nodeID=node-789, duration=2.5s
```

#### UpdateHostWithSync
- 记录更新同步的完整流程
- 区分已同步和未同步的主机

#### BatchSyncWithConsistency
- 记录批量同步的进度
- 统计成功和失败的数量
- 记录总体耗时

#### performConsistencyCheck
- 记录一致性检查的各个阶段
- 统计修复的记录数量

### 3. `src/modules/ams/jumpserver/client.go`
**主要增强的方法：**

#### doRequest
- 记录所有HTTP请求的详细信息
- 包含请求方法、URL、请求体
- 记录响应状态码、耗时、响应体长度
- 增强错误日志的详细程度

**日志示例：**
```
DEBUG: JumpServer API Request: method=POST, url=https://jumpserver.example.com/api/v1/assets/hosts/
DEBUG: JumpServer API Request Body: {"name":"web-server-01","address":"*************",...}
DEBUG: JumpServer API Response: method=POST, url=https://jumpserver.example.com/api/v1/assets/hosts/, status=201, duration=1.2s, bodyLength=456
```

### 4. `src/modules/ams/http/router_user.go`
**主要增强的方法：**

#### hostSync
- 记录HTTP请求层面的同步操作
- 统计成功和失败的主机数量
- 记录请求来源IP和总体耗时

**日志示例：**
```
INFO: HTTP: Starting host sync request: hostIDs=[123,124,125], requestIP=********
INFO: HTTP: Syncing host: hostID=123
INFO: HTTP: Host sync succeeded: hostID=123
INFO: HTTP: Host sync request completed: total=3, success=2, failed=1, duration=5.2s
```

## 日志级别说明

### INFO级别
- 主要业务流程的开始和结束
- 重要的状态变化和结果
- 统计信息和性能指标

### DEBUG级别
- 详细的配置信息
- API请求和响应的具体内容
- 内部处理逻辑的细节

### ERROR级别
- 所有错误情况的详细信息
- 包含错误上下文（主机ID、资产ID等）
- 便于问题定位和排查

## 性能监控

所有关键操作都添加了耗时统计：
- 单个主机同步耗时
- 批量同步总耗时
- API请求耗时
- 一致性检查耗时

## 故障排查支持

通过日志可以快速定位：
1. 哪个主机同步失败
2. 失败发生在哪个阶段
3. 具体的错误原因
4. API调用的详细信息
5. 同步操作的完整链路

## 使用建议

1. **生产环境**：建议将日志级别设置为INFO，确保记录关键操作而不产生过多日志
2. **调试环境**：可以设置为DEBUG级别，获取更详细的调试信息
3. **监控告警**：可以基于ERROR级别的日志设置告警规则
4. **性能分析**：通过duration字段分析同步操作的性能瓶颈
