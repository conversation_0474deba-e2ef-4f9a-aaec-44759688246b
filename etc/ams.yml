logger:
  dir: logs/ams
  level: INFO
  keepHours: 24

http:
  mode: release

i18n:
  lang: zh

# JumpServer配置
jumpserver:
  base_url: "http://10.1.4.213:8090"
  username: "admin"
  password: "password"
  token: "e7261a6a44d0a3991dbd77c9d308be3e887340b6"  # 可以使用token替代用户名密码
  organization: "Default"
  timeout: 30  # 超时时间（秒）

  # 节点配置
  node_config:
    root_node_name: "jump-ams"  # JumpServer中的根节点名称

  # 资产配置
  asset_config:
    default_platform: "Linux"  # 创建资产时的默认平台
    default_port: 22           # 默认SSH端口
    default_protocol: "ssh"    # 默认协议名称

  # 权限配置
  permission_config:
    permission_prefix: "perf-"                              # 权限规则名称前缀
    default_accounts: ["@ALL"]                              # 默认授权账号
    default_protocols: ["all"]                              # 默认授权协议
    default_expired_date: "2095-07-15T02:58:22.634069Z"    # 默认权限过期时间（永不过期）

  # 账号模板自动绑定配置
  account_config:
    # 是否启用自动账号模板绑定
    enable_auto_binding: true

    # 回退策略：当找不到同名账号模板时的处理方式
    fallback_strategy:
      # 默认账号模板名称（可选）
      default_template: "jump-ams"

      # 是否使用父节点账号模板（推荐启用）
      use_parent_template: true

    # 同步配置
    sync_config:
      # 是否在资产创建时立即绑定账号模板
      bind_on_create: true

      # 是否在资产更新时重新绑定账号模板
      rebind_on_update: false

# 使用说明：
# 1. 在 JumpServer 中预先创建账号模板，模板名称与节点路径相同
#    例如：节点路径为 "jump-ams/大兴"，则创建名为 "jump-ams/大兴" 的账号模板
# 2. 确保 JumpServer 中存在对应的节点结构
# 3. 当机器添加到AMS并同步到JumpServer时，会自动查找同名的账号模板并绑定
# 4. 如果找不到同名模板，会根据回退策略尝试父节点模板或默认模板

# 工作原理：
# 1. 机器添加到AMS后同步到JumpServer创建资产
# 2. 获取资产所在的节点路径（如 "jump-ams/大兴"）
# 3. 查找同名的账号模板（"jump-ams/大兴"）
# 4. 将找到的账号模板绑定到资产
# 5. 如果找不到，根据回退策略处理

# 配置验证：
# 1. 启动服务后，检查日志中是否有配置加载成功的信息
# 2. 访问 /api/ams-ce/accounts/sync/status 接口查看配置状态
# 3. 添加测试机器，观察是否自动绑定账号模板
# 4. 检查 JumpServer 中资产是否正确绑定了账号模板

# 故障排除：
# 1. 如果绑定失败，检查 JumpServer 中是否存在同名的账号模板
# 2. 检查节点路径是否正确，可以通过日志查看实际的节点路径
# 3. 检查 JumpServer API 权限是否足够
# 4. 查看详细的错误日志进行问题定位
