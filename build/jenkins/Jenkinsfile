pipeline {
    agent any

    stages {
        stage("init") {
            steps {
                script {
                    TIMESTAMP = sh(script: 'date +%Y%m%d_%H%M%S', returnStdout: true).trim()
                    env.BIN = "arboris"
                    env.REGISTRY = "harbor-dev.platform.baai-inner.ac.cn/arboris"
                    env.IMAGE_FULL_NAME = "${env.REGISTRY}/${env.BIN}:${env.GIT_BRANCH}"
                    ws = env.WORKSPACE
                    env.GOPROXY = "https://goproxy.cn,direct"
                    env.GO111MODULE = "on"
                }

                echo "TIMESTAMP: ${TIMESTAMP}"
                echo "Jenkins URL: ${env.JENKINS_URL}"
                echo "Build Info: id=${env.BUILD_ID}, number=${env.BUILD_NUMBER}, tag=${env.BUILD_TAG}"
                echo "The job ${env.JOB_NAME} is build in ${env.WORKSPACE} on host ${env.NODE_NAME}"
                echo "The image_name is ${env.IMAGE_FULL_NAME}"
                echo "Build info is git_branch: ${env.GIT_BRANCH} git_commit: ${env.GIT_COMMIT}"
            }
        }

        stage("build package") {
            steps {
                // 构建二进制
                sh "bash control build"
            }
        }

        stage("pack package") {
            steps {
                // 打包文件
                sh "bash control pack"
            }
        }

        stage("build images") {
            steps {
                // 构建镜像
                sh "bash control buildimage ${env.IMAGE_FULL_NAME}"
            }
        }

        stage("push") {
            steps {
                sh "docker push ${env.IMAGE_FULL_NAME}"
            }
        }
    }
}