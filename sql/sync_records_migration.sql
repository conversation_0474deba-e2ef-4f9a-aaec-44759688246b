-- 同步记录表迁移脚本
-- 用于跟踪AMS与JumpServer的同步状态

USE arboris_ams_perf;

-- 创建同步记录表
CREATE TABLE IF NOT EXISTS `sync_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `host_id` bigint(20) NOT NULL COMMENT '主机ID',
    `operation` varchar(32) NOT NULL COMMENT '操作类型: create/update/delete',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '同步状态: 0=待同步, 1=成功, 2=失败, 3=重试中',
    `asset_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'JumpServer资产ID',
    `node_id` varchar(64) NOT NULL DEFAULT '' COMMENT 'JumpServer节点ID',
    `retry_count` int(11) NOT NULL DEFAULT '0' COMMENT '重试次数',
    `last_error` text COMMENT '最后一次错误信息',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    KEY `idx_host_id` (`host_id`),
    KEY `idx_status` (`status`),
    KEY `idx_operation` (`operation`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_retry_count` (`retry_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步记录表';

-- 创建同步统计视图
CREATE OR REPLACE VIEW `sync_statistics` AS
SELECT 
    DATE(created_at) as sync_date,
    operation,
    status,
    COUNT(*) as count,
    AVG(retry_count) as avg_retry_count
FROM sync_record 
GROUP BY DATE(created_at), operation, status;

-- 创建失败同步记录视图
CREATE OR REPLACE VIEW `failed_sync_records` AS
SELECT 
    sr.*,
    h.ip as host_ip,
    h.name as host_name,
    h.ident as host_ident
FROM sync_record sr
LEFT JOIN host h ON sr.host_id = h.id
WHERE sr.status = 2 AND sr.retry_count < 3
ORDER BY sr.created_at DESC;
