FROM harbor-dev.platform.baai-inner.ac.cn/arboris/centos-tools:7
WORKDIR /home/<USER>


#RUN set -ex \
#    && mkdir -p /etc/yum.repos.d/backup \
#    && mv /etc/yum.repos.d/CentOS-* /etc/yum.repos.d/backup/ \
#    && curl -o /etc/yum.repos.d/CentOS-Base.repo http://mirrors.aliyun.com/repo/Centos-7.repo \
#    && sed -i -e '/mirrors.cloud.aliyuncs.com/d' -e '/mirrors.aliyuncs.com/d' /etc/yum.repos.d/CentOS-Base.repo \
#    && yum clean all \
#    && yum makecache \
#    && yum install -y mysql net-tools wget \
#    && yum clean all \
#    && rm -rf /var/cache/yum


COPY arboris-*.tar.gz ./arboris.tar.gz
RUN tar zxf arboris.tar.gz \
    && rm -f arboris.tar.gz


COPY entrpoint.sh .
ENTRYPOINT ./entrpoint.sh

