#!/bin/bash

# 测试JumpServer节点API的脚本

echo "=== JumpServer 节点API测试 ==="
echo

# JumpServer配置（请根据实际情况修改）
JUMPSERVER_URL="http://10.1.4.213:8090"
USERNAME="admin"
PASSWORD="password"
ORG_ID="00000000-0000-0000-0000-000000000002"

echo "JumpServer URL: $JUMPSERVER_URL"
echo "Organization: $ORG_ID"
echo

# 1. 获取所有节点
echo "=== 1. 获取所有节点 ==="
curl -s -u "$USERNAME:$PASSWORD" \
  -H "X-JMS-ORG: $ORG_ID" \
  "$JUMPSERVER_URL/api/v1/assets/nodes/" | jq '.[0:3]' || echo "请安装jq工具或检查API响应"

echo
echo

# 2. 查找大兴节点
echo "=== 2. 查找大兴节点 ==="
curl -s -u "$USERNAME:$PASSWORD" \
  -H "X-JMS-ORG: $ORG_ID" \
  "$JUMPSERVER_URL/api/v1/assets/nodes/?value=大兴" | jq '.' || echo "请安装jq工具或检查API响应"

echo
echo

# 3. 获取jump-ams根节点
echo "=== 3. 获取jump-ams根节点 ==="
curl -s -u "$USERNAME:$PASSWORD" \
  -H "X-JMS-ORG: $ORG_ID" \
  "$JUMPSERVER_URL/api/v1/assets/nodes/?value=jump-ams" | jq '.' || echo "请安装jq工具或检查API响应"

echo
echo

# 4. 如果你有具体的节点ID，可以测试获取单个节点
# 请将下面的NODE_ID替换为实际的节点ID
NODE_ID="请替换为实际的节点ID"

if [ "$NODE_ID" != "请替换为实际的节点ID" ]; then
    echo "=== 4. 获取指定节点详情 ==="
    echo "节点ID: $NODE_ID"
    curl -s -u "$USERNAME:$PASSWORD" \
      -H "X-JMS-ORG: $ORG_ID" \
      "$JUMPSERVER_URL/api/v1/assets/nodes/$NODE_ID/" | jq '.' || echo "请安装jq工具或检查API响应"
else
    echo "=== 4. 获取指定节点详情 ==="
    echo "请在脚本中设置NODE_ID变量为实际的节点ID"
fi

echo
echo

echo "=== 分析要点 ==="
echo "1. 检查节点的 'parent' 字段是否有值"
echo "2. 检查节点的 'full_path' 字段是否包含完整路径"
echo "3. 确认节点层级结构是否正确"
echo "4. 验证大兴节点的父节点是否是jump-ams"
echo
echo "=== 预期结构 ==="
echo "DEFAULT (根节点)"
echo "└── jump-ams"
echo "    ├── 大兴"
echo "    ├── 亦庄"
echo "    └── 其他节点..."
echo
echo "=== 故障排除 ==="
echo "如果节点没有parent字段或为空："
echo "1. 检查节点是否正确创建在jump-ams下"
echo "2. 确认节点层级关系是否正确"
echo "3. 检查JumpServer版本是否支持full_path字段"
echo "4. 验证API权限是否足够"
