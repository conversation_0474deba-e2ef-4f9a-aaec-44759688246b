package main

import (
	"fmt"
)

// 模拟Host结构体
type Host struct {
	Name string
	IDC  string
}

// 测试简化的路径构建逻辑
func main() {
	fmt.Println("=== 简化的节点路径构建逻辑测试 ===")
	fmt.Println()

	// 模拟配置
	rootNodeName := "jump-ams" // 这通常来自配置文件

	// 测试用例
	testCases := []struct {
		host        Host
		expected    string
		description string
	}{
		{
			host:        Host{Name: "server-001", IDC: "大兴"},
			expected:    "jump-ams/大兴",
			description: "大兴机房的服务器",
		},
		{
			host:        Host{Name: "server-002", IDC: "亦庄"},
			expected:    "jump-ams/亦庄",
			description: "亦庄机房的服务器",
		},
		{
			host:        Host{Name: "server-003", IDC: "上海"},
			expected:    "jump-ams/上海",
			description: "上海机房的服务器",
		},
		{
			host:        Host{Name: "server-004", IDC: ""},
			expected:    "jump-ams",
			description: "没有IDC信息的服务器（使用根节点）",
		},
		{
			host:        Host{Name: "test-server", IDC: "测试环境"},
			expected:    "jump-ams/测试环境",
			description: "测试环境的服务器",
		},
	}

	// 执行测试
	allPassed := true
	for i, testCase := range testCases {
		result := buildNodePathFromHost(testCase.host, rootNodeName)
		passed := result == testCase.expected
		
		status := "✅ PASS"
		if !passed {
			status = "❌ FAIL"
			allPassed = false
		}
		
		fmt.Printf("测试 %d: %s\n", i+1, testCase.description)
		fmt.Printf("  主机: %s\n", testCase.host.Name)
		fmt.Printf("  IDC: %s\n", testCase.host.IDC)
		fmt.Printf("  构建的路径: %s\n", result)
		fmt.Printf("  期望路径: %s\n", testCase.expected)
		fmt.Printf("  结果: %s\n", status)
		fmt.Println()
	}

	// 总结
	fmt.Println("=== 测试总结 ===")
	if allPassed {
		fmt.Println("✅ 所有测试通过！")
		fmt.Println()
		fmt.Println("简化的路径构建逻辑工作正常：")
		fmt.Println("- 直接根据主机的IDC字段构建路径")
		fmt.Println("- 不需要额外的API调用")
		fmt.Println("- 逻辑简单清晰，易于理解和维护")
		fmt.Println("- 避免了复杂的节点遍历和路径解析")
	} else {
		fmt.Println("❌ 部分测试失败，请检查逻辑！")
	}
	
	fmt.Println()
	fmt.Println("=== 实际问题解决 ===")
	fmt.Println("原来的问题：")
	fmt.Println("- 通过API获取节点信息，但只得到了'大兴'")
	fmt.Println("- 需要复杂的路径构建和标准化逻辑")
	fmt.Println()
	fmt.Println("现在的解决方案：")
	fmt.Println("- 直接使用主机的IDC字段：'大兴'")
	fmt.Printf("- 构建完整路径：%s\n", buildNodePathFromHost(Host{IDC: "大兴"}, rootNodeName))
	fmt.Printf("- 查找账号模板：%s\n", buildNodePathFromHost(Host{IDC: "大兴"}, rootNodeName))
	fmt.Println("- 简单、直接、可靠！")
}

// buildNodePathFromHost 根据主机信息构建节点路径（复制自服务代码）
func buildNodePathFromHost(host Host, rootNodeName string) string {
	if host.IDC != "" {
		// 有IDC信息，构建完整路径：jump-ams/IDC名称
		return rootNodeName + "/" + host.IDC
	} else {
		// 没有IDC信息，使用根节点
		return rootNodeName
	}
}

// 使用说明：
// 1. 运行命令: go run scripts/test_simplified_path_logic.go
// 2. 检查所有测试是否通过
// 3. 确认简化的逻辑符合预期
//
// 这个简化的方案解决了原来的问题：
// 1. 不再需要通过API获取节点信息
// 2. 直接根据主机的IDC字段构建路径
// 3. 逻辑简单清晰，避免了复杂的路径解析
// 4. 性能更好，减少了不必要的API调用
//
// 预期结果：
// - IDC为"大兴"的主机 -> 账号模板名称为"jump-ams/大兴"
// - IDC为"亦庄"的主机 -> 账号模板名称为"jump-ams/亦庄"
// - 这样就能正确匹配到JumpServer中对应的账号模板了！
