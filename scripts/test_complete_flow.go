package main

import (
	"fmt"
	"log"
	"time"

	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/service"
)

// 测试完整的账号模板绑定流程
func main() {
	fmt.Println("=== AMS-JumpServer 账号模板自动绑定完整流程测试 ===")
	fmt.Println()

	// 解析配置
	err := config.Parse()
	if err != nil {
		log.Fatalf("Failed to parse config: %v", err)
	}

	// 检查账号配置是否启用
	if !config.Config.JumpServer.AccountConfig.EnableAutoBinding {
		log.Println("❌ Account auto binding is disabled in config")
		log.Println("请在配置文件中启用 account_config.enable_auto_binding")
		return
	}

	log.Println("✅ 账号自动绑定功能已启用")

	// 获取JumpServer服务
	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		log.Fatalf("❌ Failed to get JumpServer service: %v", err)
	}

	log.Println("✅ 成功连接到JumpServer")
	fmt.Println()

	// 模拟真实场景的测试用例
	testScenarios := []struct {
		name        string
		assetID     string
		nodePath    string  // 模拟JumpServer中的实际节点路径
		expectedTemplate string // 期望匹配的账号模板名称
	}{
		{
			name:        "大兴机房服务器",
			assetID:     "test-server-daxing-001",
			nodePath:    "/DEFAULT/jump-ams/大兴",
			expectedTemplate: "jump-ams/大兴",
		},
		{
			name:        "亦庄机房服务器",
			assetID:     "test-server-yizhuang-001",
			nodePath:    "/DEFAULT/jump-ams/亦庄",
			expectedTemplate: "jump-ams/亦庄",
		},
		{
			name:        "大兴测试环境",
			assetID:     "test-server-daxing-test-001",
			nodePath:    "/DEFAULT/jump-ams/大兴/测试环境",
			expectedTemplate: "jump-ams/大兴/测试环境",
		},
		{
			name:        "不存在的节点（测试回退策略）",
			assetID:     "test-server-unknown-001",
			nodePath:    "/DEFAULT/jump-ams/不存在的节点",
			expectedTemplate: "jump-ams/不存在的节点",
		},
	}

	successCount := 0
	totalCount := len(testScenarios)

	for i, scenario := range testScenarios {
		fmt.Printf("=== 测试场景 %d: %s ===\n", i+1, scenario.name)
		fmt.Printf("模拟节点路径: %s\n", scenario.nodePath)
		fmt.Printf("期望账号模板: %s\n", scenario.expectedTemplate)
		fmt.Printf("测试资产ID: %s\n", scenario.assetID)
		
		// 模拟路径标准化（这里直接调用标准化逻辑）
		normalizedPath := normalizeNodePathForAccountTemplate(scenario.nodePath)
		fmt.Printf("标准化路径: %s\n", normalizedPath)
		
		if normalizedPath != scenario.expectedTemplate {
			fmt.Printf("❌ 路径标准化失败！期望: %s, 实际: %s\n", scenario.expectedTemplate, normalizedPath)
			continue
		}
		
		fmt.Printf("✅ 路径标准化正确\n")
		
		// 测试账号模板绑定
		fmt.Printf("正在测试账号绑定...\n")
		err := jsService.BindAccountTemplateToAsset(scenario.assetID, normalizedPath)
		if err != nil {
			fmt.Printf("❌ 绑定失败: %v\n", err)
			fmt.Printf("可能原因:\n")
			fmt.Printf("  - JumpServer中不存在名为 '%s' 的账号模板\n", normalizedPath)
			fmt.Printf("  - API权限不足\n")
			fmt.Printf("  - 资产ID '%s' 不存在\n", scenario.assetID)
		} else {
			fmt.Printf("✅ 绑定成功！\n")
			successCount++
		}
		
		fmt.Println()
		
		// 间隔2秒避免API限流
		if i < len(testScenarios)-1 {
			time.Sleep(2 * time.Second)
		}
	}

	// 测试总结
	fmt.Println("=== 测试总结 ===")
	fmt.Printf("总测试数: %d\n", totalCount)
	fmt.Printf("成功数: %d\n", successCount)
	fmt.Printf("失败数: %d\n", totalCount-successCount)
	
	if successCount == totalCount {
		fmt.Println("🎉 所有测试通过！账号模板自动绑定功能工作正常。")
	} else {
		fmt.Println("⚠️  部分测试失败，请检查:")
		fmt.Println("1. JumpServer中是否存在对应的账号模板")
		fmt.Println("2. API用户权限是否足够")
		fmt.Println("3. 测试资产是否存在")
	}
	
	fmt.Println()
	fmt.Println("=== 验证建议 ===")
	fmt.Println("1. 登录JumpServer管理界面")
	fmt.Println("2. 检查 账号管理 -> 账号列表")
	fmt.Println("3. 确认是否创建了新的账号实例")
	fmt.Println("4. 检查 资产管理 -> 资产列表")
	fmt.Println("5. 查看测试资产的账号绑定情况")
}

// normalizeNodePathForAccountTemplate 路径标准化逻辑（复制自服务代码）
func normalizeNodePathForAccountTemplate(fullPath string) string {
	// 去掉开头的 /
	if len(fullPath) > 0 && fullPath[0] == '/' {
		fullPath = fullPath[1:]
	}
	
	// 如果路径以 DEFAULT/ 开头，去掉这个前缀
	if len(fullPath) >= 8 && fullPath[:8] == "DEFAULT/" {
		fullPath = fullPath[8:]
	}
	
	return fullPath
}

// 使用说明：
// 1. 确保JumpServer中存在对应的账号模板
// 2. 确保配置文件中启用了账号自动绑定功能
// 3. 运行命令: go run scripts/test_complete_flow.go
//
// 这个测试验证了完整的流程：
// 1. 路径标准化：/DEFAULT/jump-ams/大兴 -> jump-ams/大兴
// 2. 账号模板查找：查找名为 "jump-ams/大兴" 的模板
// 3. 账号实例创建：根据模板创建账号实例
// 4. 账号绑定：将账号实例绑定到资产
//
// 测试成功后，应该能在JumpServer页面上看到：
// - 新创建的账号实例
// - 账号与资产的绑定关系
