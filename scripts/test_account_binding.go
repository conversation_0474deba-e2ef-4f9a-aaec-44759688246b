package main

import (
	"fmt"
	"log"
	"time"

	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/service"
)

// 测试账号模板绑定功能的简单脚本
func main() {
	// 解析配置
	err := config.Parse()
	if err != nil {
		log.Fatalf("Failed to parse config: %v", err)
	}

	// 检查账号配置是否启用
	if !config.Config.JumpServer.AccountConfig.EnableAutoBinding {
		log.Println("Account auto binding is disabled in config")
		return
	}

	// 获取JumpServer服务
	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		log.Fatalf("Failed to get JumpServer service: %v", err)
	}

	log.Println("Successfully connected to JumpServer")

	// 测试节点路径映射
	testCases := []struct {
		assetID  string
		nodePath string
	}{
		{"test-asset-1", "jump-ams/大兴"},
		{"test-asset-2", "jump-ams/亦庄"},
		{"test-asset-3", "jump-ams/test-env"},
		{"test-asset-4", "jump-ams/unknown-node"},
	}

	for _, testCase := range testCases {
		log.Printf("Testing: Asset=%s, NodePath=%s", testCase.assetID, testCase.nodePath)

		// 测试账号模板绑定
		err := jsService.BindAccountTemplateToAsset(testCase.assetID, testCase.nodePath)
		if err != nil {
			log.Printf("  Failed to bind account template: %v", err)
		} else {
			log.Printf("  Successfully bound account template")
		}

		// 间隔一秒避免API限流
		time.Sleep(1 * time.Second)
	}

	log.Println("Account template binding test completed")
}

// 使用说明：
// 1. 确保配置文件中启用了账号自动绑定功能
// 2. 确保JumpServer中存在对应名称的账号模板（如 "jump-ams/大兴"）
// 3. 运行前请先在测试环境中验证，避免影响生产数据
// 4. 运行命令: go run scripts/test_account_binding.go
//
// 注意：
// - 此脚本会测试账号模板绑定功能，请在测试环境中运行
// - 确保JumpServer中存在测试用的资产ID
// - 如果遇到权限问题，请检查JumpServer API用户的权限设置
