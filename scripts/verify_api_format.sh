#!/bin/bash

# 验证JumpServer API调用格式的脚本

echo "=== JumpServer 账号创建API格式验证 ==="
echo

# 从你提供的JumpServer页面请求中提取的正确格式
echo "正确的JumpServer账号创建API调用格式："
echo "POST /api/v1/accounts/accounts/"
echo
echo "请求体格式："
cat << 'EOF'
[
  {
    "template": "ad715157-5dea-4dca-9092-9d2e5e472939",
    "name": "jump-ams/大兴",
    "username": "",
    "secret_type": "password",
    "privileged": false,
    "asset": "3b3a2e49-c6a0-457d-9b69-9af0fab56537"
  }
]
EOF

echo
echo "=== 我们的实现对比 ==="
echo
echo "我们的实现："
echo "1. 获取账号模板信息: GET /api/v1/accounts/account-templates/{templateID}/"
echo "2. 创建账号实例: POST /api/v1/accounts/accounts/"
echo
echo "请求体结构："
cat << 'EOF'
[
  {
    "template": "{templateID}",
    "name": "{template.Name}",
    "username": "{template.Username}",
    "secret_type": "{template.AuthMethod}",
    "privileged": false,
    "asset": "{assetID}"
  }
]
EOF

echo
echo "=== 关键字段说明 ==="
echo "- template: 账号模板的ID"
echo "- name: 账号名称（通常与模板名称相同）"
echo "- username: 登录用户名（从模板获取）"
echo "- secret_type: 认证方式（password/ssh_key等）"
echo "- privileged: 是否为特权账号（通常为false）"
echo "- asset: 目标资产的ID"
echo

echo "=== 验证要点 ==="
echo "✓ 使用正确的API端点: /api/v1/accounts/accounts/"
echo "✓ 请求体为数组格式（支持批量创建）"
echo "✓ 包含必要的字段：template, name, asset"
echo "✓ 账号创建后会自动绑定到指定资产"
echo

echo "=== 测试建议 ==="
echo "1. 运行测试脚本: go run scripts/test_account_creation.go"
echo "2. 检查JumpServer日志确认API调用"
echo "3. 在JumpServer页面验证账号是否正确创建和绑定"
echo "4. 测试登录功能确认账号可用"
echo

echo "=== 故障排除 ==="
echo "如果仍然看不到绑定的账号："
echo "1. 检查JumpServer API用户是否有账号管理权限"
echo "2. 确认账号模板存在且可访问"
echo "3. 验证资产ID是否正确"
echo "4. 查看JumpServer服务端日志"
echo "5. 检查组织(Organization)设置是否正确"
