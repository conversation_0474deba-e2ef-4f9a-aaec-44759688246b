package main

import (
	"fmt"
	"log"

	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/service"
)

// 调试节点路径获取的脚本
func main() {
	fmt.Println("=== 节点路径获取调试脚本 ===")
	fmt.Println()

	// 解析配置
	err := config.Parse()
	if err != nil {
		log.Fatalf("Failed to parse config: %v", err)
	}

	// 获取JumpServer服务
	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		log.Fatalf("Failed to get JumpServer service: %v", err)
	}

	fmt.Println("✅ 成功连接到JumpServer")
	fmt.Println()

	// 这里需要一个真实的节点ID来测试
	// 你可以从JumpServer管理界面或API中获取节点ID
	testNodeID := "请替换为真实的节点ID"
	
	fmt.Printf("测试节点ID: %s\n", testNodeID)
	fmt.Println("注意: 请将上面的节点ID替换为JumpServer中真实的节点ID")
	fmt.Println()

	if testNodeID == "请替换为真实的节点ID" {
		fmt.Println("=== 如何获取节点ID ===")
		fmt.Println("1. 登录JumpServer管理界面")
		fmt.Println("2. 进入 资产管理 -> 节点管理")
		fmt.Println("3. 找到目标节点（如 大兴），点击查看详情")
		fmt.Println("4. 在URL或详情页面中找到节点ID")
		fmt.Println()
		fmt.Println("或者通过API获取:")
		fmt.Println("curl -H 'Authorization: Token your-token' \\")
		fmt.Println("     'http://jumpserver-url/api/v1/assets/nodes/'")
		fmt.Println()
		return
	}

	// 测试节点路径获取
	fmt.Println("=== 开始测试节点路径获取 ===")
	
	// 直接调用JumpServer客户端获取节点信息
	client := jsService.GetClient()
	node, err := client.GetNode(testNodeID)
	if err != nil {
		log.Fatalf("❌ 获取节点信息失败: %v", err)
	}

	fmt.Printf("节点详细信息:\n")
	fmt.Printf("  ID: %s\n", node.ID)
	fmt.Printf("  Key: %s\n", node.Key)
	fmt.Printf("  Value: %s\n", node.Value)
	fmt.Printf("  Parent: %s\n", node.Parent)
	fmt.Printf("  FullPath: %s\n", node.FullPath)
	fmt.Println()

	// 测试路径标准化
	if node.FullPath != "" {
		fmt.Printf("使用FullPath字段: %s\n", node.FullPath)
		normalizedPath := normalizeNodePathForAccountTemplate(node.FullPath)
		fmt.Printf("标准化后的路径: %s\n", normalizedPath)
		fmt.Printf("期望的账号模板名称: %s\n", normalizedPath)
	} else {
		fmt.Println("⚠️  FullPath字段为空，需要手动构建路径")
		// 这里可以添加手动构建路径的逻辑测试
	}

	fmt.Println()
	fmt.Println("=== 验证建议 ===")
	fmt.Println("1. 检查FullPath字段是否包含完整路径")
	fmt.Println("2. 确认标准化后的路径格式是否正确")
	fmt.Println("3. 验证JumpServer中是否存在对应名称的账号模板")
	fmt.Println("4. 如果FullPath为空，检查手动构建路径的逻辑")
}

// normalizeNodePathForAccountTemplate 路径标准化逻辑（复制自服务代码）
func normalizeNodePathForAccountTemplate(fullPath string) string {
	originalPath := fullPath
	
	// 去掉开头的 /
	if len(fullPath) > 0 && fullPath[0] == '/' {
		fullPath = fullPath[1:]
	}
	
	// 如果路径以 DEFAULT/ 开头，去掉这个前缀
	if len(fullPath) >= 8 && fullPath[:8] == "DEFAULT/" {
		fullPath = fullPath[8:]
	}
	
	fmt.Printf("路径标准化: '%s' -> '%s'\n", originalPath, fullPath)
	return fullPath
}

// 使用说明：
// 1. 获取JumpServer中真实的节点ID
// 2. 将脚本中的testNodeID替换为真实ID
// 3. 运行命令: go run scripts/debug_node_path.go
//
// 这个脚本会帮助你调试：
// 1. 节点信息是否正确获取
// 2. FullPath字段是否包含完整路径
// 3. 路径标准化逻辑是否正确
// 4. 最终的账号模板名称是否符合预期
//
// 预期结果：
// - 如果节点路径是 /DEFAULT/jump-ams/大兴
// - 标准化后应该是 jump-ams/大兴
// - 这就是要查找的账号模板名称
