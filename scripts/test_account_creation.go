package main

import (
	"fmt"
	"log"
	"time"

	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/service"
)

// 测试账号创建和绑定功能的脚本
func main() {
	// 解析配置
	err := config.Parse()
	if err != nil {
		log.Fatalf("Failed to parse config: %v", err)
	}

	// 检查账号配置是否启用
	if !config.Config.JumpServer.AccountConfig.EnableAutoBinding {
		log.Println("Account auto binding is disabled in config")
		return
	}

	// 获取JumpServer服务
	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		log.Fatalf("Failed to get JumpServer service: %v", err)
	}

	log.Println("Successfully connected to JumpServer")

	// 测试场景：模拟真实的资产和节点路径
	testCases := []struct {
		description string
		assetID     string
		nodePath    string
	}{
		{
			description: "测试大兴节点账号模板绑定",
			assetID:     "test-asset-daxing-001",
			nodePath:    "jump-ams/大兴",
		},
		{
			description: "测试亦庄节点账号模板绑定",
			assetID:     "test-asset-yizhuang-001", 
			nodePath:    "jump-ams/亦庄",
		},
		{
			description: "测试不存在的节点（应该触发回退策略）",
			assetID:     "test-asset-unknown-001",
			nodePath:    "jump-ams/不存在的节点",
		},
	}

	for i, testCase := range testCases {
		log.Printf("\n=== 测试 %d: %s ===", i+1, testCase.description)
		log.Printf("资产ID: %s", testCase.assetID)
		log.Printf("节点路径: %s", testCase.nodePath)
		
		// 测试账号模板绑定
		err := jsService.BindAccountTemplateToAsset(testCase.assetID, testCase.nodePath)
		if err != nil {
			log.Printf("❌ 绑定失败: %v", err)
		} else {
			log.Printf("✅ 绑定成功")
		}
		
		// 间隔2秒避免API限流
		if i < len(testCases)-1 {
			time.Sleep(2 * time.Second)
		}
	}

	log.Println("\n=== 测试完成 ===")
	log.Println("请检查JumpServer页面确认账号是否正确创建和绑定")
	log.Println("提示：")
	log.Println("1. 登录JumpServer管理界面")
	log.Println("2. 进入 资产管理 -> 资产列表")
	log.Println("3. 找到对应的测试资产")
	log.Println("4. 查看资产的账号绑定情况")
}

// 使用说明：
// 1. 确保JumpServer中存在对应的账号模板（如 "jump-ams/大兴"）
// 2. 确保配置文件中启用了账号自动绑定功能
// 3. 运行前请确认测试资产ID不会与生产环境冲突
// 4. 运行命令: go run scripts/test_account_creation.go
//
// 注意：
// - 此脚本会在JumpServer中创建真实的账号实例
// - 请在测试环境中运行，避免影响生产数据
// - 测试完成后请及时清理测试数据
// - 如果遇到权限问题，请检查JumpServer API用户的权限设置
//
// 预期结果：
// - 如果JumpServer中存在对应的账号模板，应该能成功创建账号并绑定到资产
// - 在JumpServer页面上应该能看到新创建的账号
// - 账号名称应该与节点路径相同（如 "jump-ams/大兴"）
