package main

import (
	"fmt"
	"strings"
)

// normalizeNodePathForAccountTemplate 将节点路径标准化为账号模板名称格式
// 这是从 jumpserver.go 中复制的逻辑，用于独立测试
func normalizeNodePathForAccountTemplate(fullPath string) string {
	// 去掉开头的 /
	if strings.HasPrefix(fullPath, "/") {
		fullPath = fullPath[1:]
	}
	
	// 如果路径以 DEFAULT/ 开头，去掉这个前缀
	if strings.HasPrefix(fullPath, "DEFAULT/") {
		fullPath = fullPath[8:] // len("DEFAULT/") = 8
	}
	
	return fullPath
}

func main() {
	fmt.Println("=== 节点路径标准化测试 ===")
	fmt.Println()

	// 测试用例
	testCases := []struct {
		input    string
		expected string
		description string
	}{
		{
			input:    "/DEFAULT/jump-ams/大兴",
			expected: "jump-ams/大兴",
			description: "标准情况：去掉 /DEFAULT/ 前缀",
		},
		{
			input:    "/DEFAULT/jump-ams/亦庄",
			expected: "jump-ams/亦庄",
			description: "亦庄节点测试",
		},
		{
			input:    "/DEFAULT/jump-ams/大兴/测试环境",
			expected: "jump-ams/大兴/测试环境",
			description: "多级节点路径",
		},
		{
			input:    "DEFAULT/jump-ams/上海",
			expected: "jump-ams/上海",
			description: "没有开头斜杠的情况",
		},
		{
			input:    "/jump-ams/深圳",
			expected: "jump-ams/深圳",
			description: "没有 DEFAULT 前缀的情况",
		},
		{
			input:    "jump-ams/广州",
			expected: "jump-ams/广州",
			description: "已经是标准格式",
		},
		{
			input:    "/DEFAULT/",
			expected: "",
			description: "只有 DEFAULT 的情况",
		},
		{
			input:    "/DEFAULT/jump-ams",
			expected: "jump-ams",
			description: "只到 jump-ams 根节点",
		},
	}

	// 执行测试
	allPassed := true
	for i, testCase := range testCases {
		result := normalizeNodePathForAccountTemplate(testCase.input)
		passed := result == testCase.expected
		
		status := "✅ PASS"
		if !passed {
			status = "❌ FAIL"
			allPassed = false
		}
		
		fmt.Printf("测试 %d: %s\n", i+1, testCase.description)
		fmt.Printf("  输入: %s\n", testCase.input)
		fmt.Printf("  期望: %s\n", testCase.expected)
		fmt.Printf("  实际: %s\n", result)
		fmt.Printf("  结果: %s\n", status)
		fmt.Println()
	}

	// 总结
	fmt.Println("=== 测试总结 ===")
	if allPassed {
		fmt.Println("✅ 所有测试通过！")
		fmt.Println()
		fmt.Println("路径标准化逻辑工作正常：")
		fmt.Println("- 正确去掉了 /DEFAULT/ 前缀")
		fmt.Println("- 保留了 jump-ams 及其子路径")
		fmt.Println("- 处理了各种边界情况")
	} else {
		fmt.Println("❌ 部分测试失败，请检查逻辑！")
	}
	
	fmt.Println()
	fmt.Println("=== 实际使用示例 ===")
	fmt.Println("当机器挂载到节点 '/DEFAULT/jump-ams/大兴' 时：")
	fmt.Printf("- 原始路径: /DEFAULT/jump-ams/大兴\n")
	fmt.Printf("- 标准化后: %s\n", normalizeNodePathForAccountTemplate("/DEFAULT/jump-ams/大兴"))
	fmt.Printf("- 查找的账号模板名称: %s\n", normalizeNodePathForAccountTemplate("/DEFAULT/jump-ams/大兴"))
	fmt.Println("- 这样就能正确匹配到 JumpServer 中名为 'jump-ams/大兴' 的账号模板了！")
}

// 使用说明：
// 1. 运行命令: go run scripts/test_node_path_normalization.go
// 2. 检查所有测试是否通过
// 3. 确认路径标准化逻辑符合预期
//
// 这个测试验证了节点路径标准化的逻辑：
// - 输入: /DEFAULT/jump-ams/大兴 (JumpServer 中的实际节点路径)
// - 输出: jump-ams/大兴 (账号模板名称)
//
// 这样就能正确匹配到 JumpServer 中的账号模板了！
