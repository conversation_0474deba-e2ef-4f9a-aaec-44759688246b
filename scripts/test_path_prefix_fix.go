package main

import (
	"fmt"
	"strings"
)

// 测试路径前缀修复逻辑
func main() {
	fmt.Println("=== 路径前缀修复逻辑测试 ===")
	fmt.Println()

	// 测试用例
	testCases := []struct {
		input       string
		expected    string
		description string
	}{
		{
			input:       "大兴",
			expected:    "jump-ams/大兴",
			description: "单独的节点名称（当前问题场景）",
		},
		{
			input:       "亦庄",
			expected:    "jump-ams/亦庄",
			description: "另一个单独的节点名称",
		},
		{
			input:       "jump-ams/大兴",
			expected:    "jump-ams/大兴",
			description: "已经有正确前缀的路径",
		},
		{
			input:       "jump-ams",
			expected:    "jump-ams",
			description: "jump-ams根节点本身",
		},
		{
			input:       "jump-ams/大兴/测试环境",
			expected:    "jump-ams/大兴/测试环境",
			description: "多级路径",
		},
		{
			input:       "",
			expected:    "",
			description: "空路径",
		},
		{
			input:       "DEFAULT/jump-ams/大兴",
			expected:    "jump-ams/大兴",
			description: "包含DEFAULT前缀的路径",
		},
		{
			input:       "/DEFAULT/jump-ams/大兴",
			expected:    "jump-ams/大兴",
			description: "完整的原始路径",
		},
	}

	// 执行测试
	allPassed := true
	for i, testCase := range testCases {
		// 先进行标准化处理
		normalized := normalizeNodePathForAccountTemplate(testCase.input)
		
		// 然后应用前缀修复逻辑
		result := applyJumpAmsPrefixFix(normalized)
		
		passed := result == testCase.expected
		
		status := "✅ PASS"
		if !passed {
			status = "❌ FAIL"
			allPassed = false
		}
		
		fmt.Printf("测试 %d: %s\n", i+1, testCase.description)
		fmt.Printf("  输入: '%s'\n", testCase.input)
		fmt.Printf("  标准化: '%s'\n", normalized)
		fmt.Printf("  前缀修复: '%s'\n", result)
		fmt.Printf("  期望: '%s'\n", testCase.expected)
		fmt.Printf("  结果: %s\n", status)
		fmt.Println()
	}

	// 总结
	fmt.Println("=== 测试总结 ===")
	if allPassed {
		fmt.Println("✅ 所有测试通过！")
		fmt.Println()
		fmt.Println("前缀修复逻辑工作正常：")
		fmt.Println("- 正确处理了单独的节点名称")
		fmt.Println("- 保留了已有正确前缀的路径")
		fmt.Println("- 不影响jump-ams根节点")
		fmt.Println("- 正确处理了各种边界情况")
	} else {
		fmt.Println("❌ 部分测试失败，请检查逻辑！")
	}
	
	fmt.Println()
	fmt.Println("=== 实际问题修复 ===")
	fmt.Println("根据日志分析，当前问题是：")
	fmt.Printf("- 原始路径: 大兴\n")
	fmt.Printf("- 修复后路径: %s\n", applyJumpAmsPrefixFix(normalizeNodePathForAccountTemplate("大兴")))
	fmt.Printf("- 查找的账号模板: %s\n", applyJumpAmsPrefixFix(normalizeNodePathForAccountTemplate("大兴")))
	fmt.Println("- 这样就能正确匹配到 JumpServer 中名为 'jump-ams/大兴' 的账号模板了！")
}

// normalizeNodePathForAccountTemplate 路径标准化逻辑（复制自服务代码）
func normalizeNodePathForAccountTemplate(fullPath string) string {
	// 去掉开头的 /
	if strings.HasPrefix(fullPath, "/") {
		fullPath = fullPath[1:]
	}
	
	// 如果路径以 DEFAULT/ 开头，去掉这个前缀
	if strings.HasPrefix(fullPath, "DEFAULT/") {
		fullPath = fullPath[8:] // len("DEFAULT/") = 8
	}
	
	return fullPath
}

// applyJumpAmsPrefixFix 应用jump-ams前缀修复逻辑（复制自服务代码）
func applyJumpAmsPrefixFix(normalizedPath string) string {
	// 如果标准化后的路径不包含jump-ams前缀，且不是jump-ams本身，则添加前缀
	if normalizedPath != "" && normalizedPath != "jump-ams" && !strings.HasPrefix(normalizedPath, "jump-ams/") {
		return "jump-ams/" + normalizedPath
	}
	return normalizedPath
}

// 使用说明：
// 1. 运行命令: go run scripts/test_path_prefix_fix.go
// 2. 检查所有测试是否通过
// 3. 确认前缀修复逻辑符合预期
//
// 这个修复解决了节点路径不完整的问题：
// - 当节点直接挂在根节点下时（如只返回"大兴"）
// - 自动添加"jump-ams/"前缀
// - 最终得到正确的账号模板名称"jump-ams/大兴"
