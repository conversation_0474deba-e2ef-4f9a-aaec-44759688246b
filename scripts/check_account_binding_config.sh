#!/bin/bash

# AMS 账号模板自动绑定功能配置检查脚本

set -e

echo "=== AMS 账号模板自动绑定功能配置检查 ==="
echo

# 检查配置文件
echo "1. 检查配置文件..."
CONFIG_FILE=""
if [ -f "etc/ams.local.yml" ]; then
    CONFIG_FILE="etc/ams.local.yml"
    echo "   ✓ 找到配置文件: etc/ams.local.yml"
elif [ -f "etc/ams.yml" ]; then
    CONFIG_FILE="etc/ams.yml"
    echo "   ✓ 找到配置文件: etc/ams.yml"
else
    echo "   ✗ 未找到配置文件 (etc/ams.yml 或 etc/ams.local.yml)"
    exit 1
fi

# 检查JumpServer基础配置
echo
echo "2. 检查JumpServer基础配置..."
if grep -q "jumpserver:" "$CONFIG_FILE"; then
    echo "   ✓ 找到JumpServer配置段"
else
    echo "   ✗ 未找到JumpServer配置段"
    exit 1
fi

# 检查必要的JumpServer配置项
REQUIRED_FIELDS=("base_url" "username" "password" "organization")
for field in "${REQUIRED_FIELDS[@]}"; do
    if grep -q "$field:" "$CONFIG_FILE"; then
        echo "   ✓ 找到必要配置: $field"
    else
        echo "   ✗ 缺少必要配置: $field"
        exit 1
    fi
done

# 检查账号配置
echo
echo "3. 检查账号模板配置..."
if grep -q "account_config:" "$CONFIG_FILE"; then
    echo "   ✓ 找到账号配置段"
else
    echo "   ✗ 未找到账号配置段"
    echo "   提示: 请参考 etc/ams.account.example.yml 添加账号配置"
    exit 1
fi

# 检查账号配置的关键字段
ACCOUNT_FIELDS=("enable_auto_binding" "node_path_mapping" "fallback_strategy")
for field in "${ACCOUNT_FIELDS[@]}"; do
    if grep -q "$field:" "$CONFIG_FILE"; then
        echo "   ✓ 找到账号配置: $field"
    else
        echo "   ⚠ 缺少账号配置: $field (将使用默认值)"
    fi
done

# 检查源代码文件
echo
echo "4. 检查源代码文件..."
SOURCE_FILES=(
    "src/modules/ams/config/yaml.go"
    "src/modules/ams/jumpserver/types.go"
    "src/modules/ams/jumpserver/client.go"
    "src/modules/ams/service/account_template_manager.go"
    "src/modules/ams/service/account_binding_service.go"
    "src/modules/ams/service/jumpserver.go"
    "src/modules/ams/http/account_management.go"
    "src/modules/ams/http/router.go"
)

for file in "${SOURCE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "   ✓ 找到源文件: $file"
    else
        echo "   ✗ 缺少源文件: $file"
        exit 1
    fi
done

# 检查Go模块依赖
echo
echo "5. 检查Go模块依赖..."
if [ -f "go.mod" ]; then
    echo "   ✓ 找到go.mod文件"
    
    # 检查关键依赖
    REQUIRED_DEPS=("github.com/gin-gonic/gin" "github.com/toolkits/pkg")
    for dep in "${REQUIRED_DEPS[@]}"; do
        if grep -q "$dep" go.mod; then
            echo "   ✓ 找到依赖: $dep"
        else
            echo "   ⚠ 未找到依赖: $dep"
        fi
    done
else
    echo "   ✗ 未找到go.mod文件"
    exit 1
fi

# 检查编译
echo
echo "6. 检查代码编译..."
if go build -o /tmp/ams-test ./src/modules/ams/ 2>/dev/null; then
    echo "   ✓ 代码编译成功"
    rm -f /tmp/ams-test
else
    echo "   ✗ 代码编译失败"
    echo "   请运行 'go build ./src/modules/ams/' 查看详细错误信息"
    exit 1
fi

# 提供配置建议
echo
echo "=== 配置建议 ==="
echo
echo "1. 确保在JumpServer中创建了对应的账号模板"
echo "2. 验证JumpServer API用户具有足够的权限"
echo "3. 测试JumpServer连接: curl -u username:password http://jumpserver-url/api/v1/users/profile/"
echo "4. 参考配置示例: etc/ams.account.example.yml"
echo "5. 查看使用文档: docs-ams/Account_Template_Auto_Binding_README.md"
echo

# 提供测试命令
echo "=== 测试命令 ==="
echo
echo "1. 启动服务后检查配置状态:"
echo "   curl -H 'Authorization: Bearer <token>' http://localhost:port/api/ams-ce/accounts/sync/status"
echo
echo "2. 测试账号绑定功能:"
echo "   go run scripts/test_account_binding.go"
echo
echo "3. 查看服务日志:"
echo "   tail -f logs/ams.log | grep -i account"
echo

echo "=== 检查完成 ==="
echo "✓ 所有必要文件和配置检查通过"
echo "现在可以启动服务并测试账号模板自动绑定功能"
