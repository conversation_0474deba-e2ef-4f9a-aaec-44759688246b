#!/bin/bash

# 生成AMS模块的Swagger文档
# 确保不包含RDB模块的内容

echo "=== 生成AMS模块Swagger文档 ==="

# 检查swag工具是否存在
if ! command -v ~/go/bin/swag &> /dev/null; then
    echo "swag工具未找到，正在安装..."
    go install github.com/swaggo/swag/cmd/swag@latest
    if [ $? -ne 0 ]; then
        echo "安装swag工具失败"
        exit 1
    fi
fi

# 进入AMS模块目录
cd src/modules/ams

# 生成swagger文档
echo "正在生成swagger文档..."
~/go/bin/swag init \
    --generalInfo ams.go \
    --dir . \
    --output ../../../docs-ams \
    --parseDependency \
    --parseInternal \
    --exclude ../rdb

if [ $? -eq 0 ]; then
    echo "✅ AMS模块Swagger文档生成成功！"
    echo "📁 输出目录: docs-ams/"
    echo "🌐 访问地址: http://localhost:8002/swagger/index.html"
    echo ""
    echo "生成的文件:"
    ls -la ../../../docs-ams/
else
    echo "❌ Swagger文档生成失败"
    exit 1
fi

# 检查是否意外包含了RDB模块的内容
if grep -q "rdb" ../../../docs-ams/swagger.json; then
    echo "⚠️  警告: 文档中可能包含RDB模块内容"
else
    echo "✅ 文档中未包含RDB模块内容"
fi

echo ""
echo "=== 文档统计 ==="
echo "API路径数量: $(grep -c '"paths"' ../../../docs-ams/swagger.json)"
echo "定义数量: $(grep -c '"definitions"' ../../../docs-ams/swagger.json)"

echo ""
echo "🎉 AMS模块Swagger文档生成完成！"
