# AMS-JumpServer 账号模板自动绑定功能

## 功能概述

AMS-JumpServer 账号模板自动绑定功能可以在机器添加到AMS后，自动根据机器在JumpServer中的节点路径绑定相应的账号模板，实现用户无需手动配置即可登录新添加的机器。

## 核心特性

- **自动绑定**：机器添加后自动绑定账号，无需手动干预
- **节点路径映射**：根据机器所在的JumpServer节点路径自动选择账号模板
- **灵活配置**：支持精确匹配、模式匹配和回退策略
- **批量操作**：支持批量绑定和同步缺失的账号
- **错误处理**：完善的重试机制和错误处理
- **监控接口**：提供API接口查看绑定状态和配置信息

## 配置说明

### 1. 基础配置

在 `etc/ams.yml` 或 `etc/ams.local.yml` 中添加以下配置：

```yaml
jumpserver:
  # ... 其他JumpServer配置 ...
  
  account_config:
    # 启用自动账号绑定
    enable_auto_binding: true
    
    # 映射策略
    template_mapping_strategy: "node_path"
    
    # 节点路径映射
    node_path_mapping:
      exact_match:
        "jump-ams/大兴": "jump-ams/大兴"
        "jump-ams/亦庄": "jump-ams/亦庄"
      pattern_match:
        "jump-ams/test-*": "jump-ams/test-common"
    
    # 回退策略
    fallback_strategy:
      default_template: "jump-ams/default"
      use_parent_template: true
      create_missing_template: false
    
    # 默认账号模板配置
    default_account_template:
      username: "root"
      auth_method: "password"
    
    # 重试配置
    binding_retry:
      max_attempts: 3
      retry_interval: 5
    
    # 同步配置
    sync_config:
      bind_on_create: true
      rebind_on_update: false
      batch_size: 10
```

### 2. JumpServer准备工作

在使用此功能前，需要在JumpServer中完成以下准备：

1. **创建账号模板**
   - 在JumpServer管理界面中创建对应的账号模板
   - 模板名称需要与配置文件中的名称一致
   - 配置账号的用户名、认证方式等信息

2. **确保节点结构**
   - 确保JumpServer中存在对应的节点结构
   - 节点路径格式：`jump-ams/子节点名称`

3. **API权限**
   - 确保AMS使用的JumpServer API用户具有足够的权限
   - 需要账号管理和资产管理权限

## 使用方法

### 1. 自动绑定

启用配置后，当通过AMS添加新机器时，系统会自动：
1. 将机器同步到JumpServer作为资产
2. 根据机器所在节点路径查找对应的账号模板
3. 创建或获取账号实例
4. 将账号绑定到资产

### 2. 手动批量绑定

通过API接口进行批量绑定：

```bash
curl -X POST "http://your-ams-server/api/ams-ce/accounts/bindings/batch" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "asset_id": "asset-id-1",
      "node_path": "jump-ams/大兴"
    },
    {
      "asset_id": "asset-id-2", 
      "node_path": "jump-ams/亦庄"
    }
  ]'
```

### 3. 同步缺失的账号绑定

```bash
curl -X POST "http://your-ams-server/api/ams-ce/accounts/sync/missing" \
  -H "Authorization: Bearer your-token"
```

## API接口

### 1. 获取账号同步状态

```
GET /api/ams-ce/accounts/sync/status
```

返回当前的配置状态和统计信息。

### 2. 获取账号模板列表

```
GET /api/ams-ce/accounts/templates
```

返回已配置的账号模板信息。

### 3. 获取资产账号绑定

```
GET /api/ams-ce/accounts/bindings?asset_id=<asset_id>
```

返回指定资产绑定的账号列表。

### 4. 解绑账号

```
DELETE /api/ams-ce/accounts/bindings?asset_id=<asset_id>&account_id=<account_id>
```

解绑指定资产的账号。

### 5. 批量绑定账号

```
POST /api/ams-ce/accounts/bindings/batch
```

批量绑定账号到资产。

### 6. 同步缺失账号

```
POST /api/ams-ce/accounts/sync/missing
```

检查并同步所有缺失账号绑定的资产。

## 故障排除

### 1. 绑定失败

**问题**：账号绑定失败
**解决方案**：
- 检查JumpServer中是否存在对应的账号模板
- 验证节点路径是否正确
- 检查API用户权限
- 查看详细错误日志

### 2. 找不到账号模板

**问题**：提示找不到账号模板
**解决方案**：
- 确认JumpServer中已创建对应名称的账号模板
- 检查配置文件中的模板名称是否正确
- 验证回退策略配置

### 3. 节点路径不匹配

**问题**：节点路径无法匹配到账号模板
**解决方案**：
- 检查机器在JumpServer中的实际节点路径
- 调整配置文件中的映射规则
- 启用父节点回退策略

### 4. API权限不足

**问题**：API调用返回权限错误
**解决方案**：
- 检查JumpServer API用户的权限设置
- 确保用户具有账号管理和资产管理权限
- 验证API Token是否有效

## 监控和日志

### 1. 日志级别

系统会记录以下级别的日志：
- **INFO**：正常的绑定操作
- **WARNING**：非致命错误和警告
- **ERROR**：绑定失败和错误信息
- **DEBUG**：详细的调试信息

### 2. 关键日志

```
# 成功绑定
Successfully bound account xxx to asset yyy

# 绑定失败
Failed to bind account to asset xxx: error message

# 模板匹配
Found account template: xxx for node path: yyy

# 回退策略
Using default template: xxx
```

### 3. 监控指标

通过API接口可以获取以下监控指标：
- 自动绑定启用状态
- 配置的映射规则数量
- 绑定成功率
- 错误统计

## 最佳实践

### 1. 命名规范

- 账号模板名称建议与节点路径保持一致
- 使用有意义的命名，便于管理和维护
- 避免使用特殊字符和空格

### 2. 权限管理

- 为不同环境配置不同的账号模板
- 定期审查和更新账号权限
- 使用最小权限原则

### 3. 监控告警

- 定期检查绑定成功率
- 设置绑定失败告警
- 监控API调用频率和错误率

### 4. 测试验证

- 在测试环境中验证配置
- 定期测试回退策略
- 验证批量操作性能

## 版本兼容性

- AMS版本：v1.0+
- JumpServer版本：v2.6+
- Go版本：1.16+

## 技术支持

如遇到问题，请：
1. 查看系统日志获取详细错误信息
2. 检查配置文件语法和内容
3. 验证JumpServer连接和权限
4. 参考故障排除指南

更多技术细节请参考：`AMS_JumpServer_Account_Template_Auto_Binding_Solution.md`
